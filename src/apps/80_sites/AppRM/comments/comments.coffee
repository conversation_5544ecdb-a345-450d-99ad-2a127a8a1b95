UserModel = MODEL 'User'
getConfig = DEF 'getConfig'
# accessAllowed = DEF '_access_allowed'
helpers = INCLUDE 'lib.helpers'
{formatCmntTs} = INCLUDE 'lib.helpers_date'
dataMethods = DEF 'dataMethods'
debug =DEBUG()
ForumModel = MODEL 'Forum'
BlockPhoneModel = MODEL 'BlockPhone'

wordpressHelper = INCLUDE 'libapp.wordpressHelper'
addWpComment = wordpressHelper.addWpComment
SysNotifyModel = MODEL 'SysNotify'
{respError} = INCLUDE 'libapp.responseHelper'
checkTextContent = DEF 'checkTextContent'
PromptsModel = MODEL 'Prompts'

processId =(req, id)->
  return id unless req.param('col') is 'sch'
  return id unless 'string' is typeof id
  return parseInt(id) or id

isGroupAdmin=(req, user, gid, cb)->
  return cb(false) unless gid
  req.userGroups user, (err, groups)->
    return cb(false) unless gid and groups
    grp = groups.find((g) ->
      return g._id.toString() is gid
    )
    if grp && (grp.isAdmin or grp.isOwner)
      return cb(true)
    else
      return cb(false)

setCmntThumbUpAndRating = (ratings, user, cmnt, cmntidx)->
  cmnt.tupcnt = cmnt.tupcnt or 0
  unless user and (ratings?.length)
    cmnt.tup = false
    return
  for rate in ratings
    if rate.uid.toString() is cmnt.uid.toString()
      cmnt.r = rate.r
    if rate.uid.toString() is user._id.toString()
      if rate[cmntidx.toString()] is true
        cmnt.tup = true
      else
        cmnt.tup = false

sortCmnt = (cmnt1, cmnt2) ->
  return -1 if (cmnt1.sticky and (not cmnt2.sticky))
  return 1 if ((not cmnt1.sticky) and cmnt2.sticky)
  if cmnt1.sticky and cmnt2.sticky
    return (new Date(cmnt2.mt) - new Date(cmnt1.mt))

processCmnts = (post, req)->
  if post.cmnts
    post.cmnts.sort sortCmnt
    l10n = (a,b)->req.l10n a,b
    user = UserModel.getFromSession req#retObj.user
    for cmnt,idx in post.cmnts
      cmnt.sticky = cmnt.sticky or false
      cmnt.del = cmnt.del or false
      cmnt.ts = formatCmntTs(cmnt.ts,l10n) if (req.getDevType() is 'browser')
      # 处理评论原文的HTML转义
      cmnt.origM = cmnt.origM?.replace(/&lt;/g, '<').replace(/&gt;/g, '>') if cmnt.origM
      setCmntThumbUpAndRating post.rating, user, cmnt, idx
getMyRating = (post, user)->
  unless post?.rating?.length
    post.myRating = 0
    post.avgRating = 0
    post.avgRoundRating = 0
    post.totalCnt = 0
    return
  totoalRating = 0
  totalCnt = 0
  user = UserModel.getFromSession req
  for rate in post.rating
    unless rate.r
      continue
    totoalRating = totoalRating + rate.r
    totalCnt++
    if user and (rate.uid.toString() is user._id.toString())
      post.myRating = rate.r
  if totoalRating
    post.totalCnt = totalCnt
    post.avgRating = Math.round(totoalRating*10/totalCnt)/10
    post.avgRoundRating = Math.floor((post.avgRating+0.25)*2)/2
  else
    post.totalCnt = 0
    post.avgRating = post.avgRoundRating = 0
DEF 'processCmnts',processCmnts
DEF 'getMyRating',getMyRating
DEF 'setCmntThumbUpAndRating', setCmntThumbUpAndRating
DEF 'isGroupAdmin', isGroupAdmin
# DEF 'setReadersStaus', setReadersStaus

APP '1.5/fav'
POST ':id', (req, resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless user
    id = req.param("id")
    id = processId req, id
    {gid,fav} = params
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id and typeof fav is 'boolean'
    ForumModel.favourite {id,fav,gid,user}, (err,ret)->
      # return respError req.l10n(MSG_STRINGS.DB_ERROR), resp,'db', err
      if err
        clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
        SysNotifyModel.notifyAdmin err?.toString()+clientMsg
        return respError {
          clientMsg,
          resp,
          sysErr:err}
      return resp.send {ok:1}

APP '1.5/rating'
onRatingSuccess = (ret, req, resp)->
  post = ret.value
  getMyRating post, req
  processCmnts post, req
  return resp.send {ok:1,cmnts:post.cmnts,totalCnt:post.totalCnt, avgRating:post.avgRating, avgRoundRating:post.avgRoundRating}

POST ':id', (req, resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless user
    id = req.param('id')
    id = processId req, id
    rating = parseInt(params.rating)
    gid = params.gid
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id and rating
    ForumModel.updateUserRating {id,rating,user,gid},(err,ret)->
      if err
        clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
        SysNotifyModel.notifyAdmin err?.toString()+clientMsg
        return respError {
          clientMsg,
          resp,
          sysErr:err}
      colName = if gid then 'GroupForumCol' else 'Forum'
      unless ret.value
        errorMsg = "can not find record #{id} of #{colName}  when rating"
        debug.error errorMsg
        clientMsg = req.l10n(MSG_STRINGS.NOT_FOUND)
        SysNotifyModel.notifyAdmin err?.toString()+clientMsg
        return respError {
          clientMsg,
          resp,
          sysErr:errorMsg}
      onRatingSuccess(ret, req, resp)

APP '1.5/comment'
POST 'disableCmnt/:id', (req, resp) ->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
  UserModel.appAuth {req,resp},(user)->
    isGroupAdmin req, user, params.gid,(groupAdmin)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless user and (groupAdmin or UserModel.accessAllowed 'forumAdmin',user)
      id = req.param("id")
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id
      {gid,discmnt} = params
      ForumModel.disableComment {id,discmnt,gid},(err,ret)->
        # return respError req.l10n(MSG_STRINGS.DB_ERROR), resp,'db', err
        if err
          clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
          SysNotifyModel.notifyAdmin err?.toString()+clientMsg
          return respError {
            clientMsg,
            resp,
            sysErr:err}
        #return respError req.l10n('discmnt post failed'), resp unless ret.lastErrorObject.n
        return resp.send {ok:1}

delCmnt = (id, del, index, req, resp, params) ->
  gid = params.gid
  ForumModel.deleteComment {id,del,index,gid},(err,ret)->
    if err
      clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
      SysNotifyModel.notifyAdmin err?.toString()+clientMsg
      return respError {
        clientMsg,
        resp,
        sysErr:err}
    # if req.param('col') in ['psch','sch']
    #   update = {$inc: inc}
    #   updateForumCounter req, cmnt,update, (err, result)->
    #     return respError req.l10n(MSG_STRINGS.DB_ERROR), resp, err if err
    wpHosts  = params.wpHosts
    cmnts = ret.cmnts
    post_wpHosts = ret.wpHosts
    ForumModel.handleCommentForWordpress {id,del,gid,index,cmnts,wpHosts,post_wpHosts}
    return resp.send {ok:1, cmntl: ret.cmntl}

#update forum collection, update counter and cmntl when add cmnts to school
# updateForumCounter = (req, cmnt, update, cb)->
#   id = req.param("id")
#   #id in forum is string or objectid
#   ForumCol.findOneAndUpdate {_id: id}, update,{upsert:false,returnDocument:'after'}, (err,ret)->
#     return cb err

# /1.5/comment/:id
POST ':id', (req, resp)->
  id = req.param("id")
  id = processId req, id
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless params = req.body
  action = params.action
  src = params.src
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless id and action
  comment = params.comment
  index = +(params.index)
  sticky = params.sticky
  tup = params.tup
  ref = params.ref
  del = params.del
  gid = params.gid
  cmnt = {}
  city = params.city
  prov = params.prov
  UserModel.appAuth {req,resp,userfields:'all'},(user)->
    isGroupAdmin req, user, gid,(groupAdmin)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN), resp} unless user
      if action is 'del'
        return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp} unless groupAdmin or UserModel.accessAllowed 'forumAdmin',user
        return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless helpers.isNumber index
        return delCmnt id, del, index, req, resp, params
      else if action is 'add'
        return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),resp} if user.forBlk
        return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless comment
        UserModel.isForumCommentBlocked {user}, (ret) ->
          if ret?[0]
            ret2 = {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),category:MSG_STRINGS.DEV_FORUM_BLOCKED,resp}
            if ret?[1] is MSG_STRINGS.EMAIL_NOT_VERIFIED
              # todo 重复翻译
              ret2.clientMsg = req.l10n 'Please verify your email address in order to publish posts.'
              ret2.category = MSG_STRINGS.EMAIL_NOT_VERIFIED
            return respError ret2
          if user.mbl
            try
              isPhoneNumberBlocked = await BlockPhoneModel.isPhoneNumberBlocked user.mbl
            catch err
              console.error err
              return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp}
            if isPhoneNumberBlocked
              return respError {clientMsg:req.l10n(MSG_STRINGS.PHONE_BLOCKED), resp}
          comment = helpers.htmlEscape comment
          cmnt = {uid: user._id, m:comment, fornm:user.fornm, ts: new Date()}
          cmnt.avt = user.avt if user.avt
          cmnt.ref = ref if helpers.isNumber ref
          checkContentObj =
            l10n:(a,b)->req.l10n a,b
            user:user
            id: id
            collection:'forum'
            content:comment

          # 统一的审核结果处理函数
          handleModerationResult = (err, ret) ->
            if err
              console.error err
              return resp.send {ok:0, e:err.toString()}
            return resp.send {ok:0, e:ret.msg} if ret?.block
            return respError {clientMsg:req.l10n('no content'), resp} unless cmnt?.m
            return respError {clientMsg:req.l10n('no fornm'), resp} unless cmnt?.fornm

            # 获取用户语言和翻译模板
            if helpers.hasChinese(cmnt.m)
              userLanguage = 'zh-cn'
            else
              userLanguage = req.locale() or 'en'

            # 获取翻译模板
            try
              translationTemplates = await PromptsModel.getCachedPromptTemplates('comment_translation')
            catch error
              debug.error '获取评论翻译模板失败:', error
              translationTemplates = []

            # 调用addComment，传递翻译相关参数
            ForumModel.addComment {
              id,
              cmnt,
              src,
              user,
              gid,
              userLanguage,
              translationTemplates
            }, (err, post) ->
              return resp.send {ok:0, e:req.l10n(err, 'forum')} if err
              resp.send {ok:1, cmnts:post.cmnts, cmntl: post.cmntl}
              if gid = req.param 'gid'
                ForumModel.setReadersStaus {gid,id},(err, result)->
                  if err
                    clientMsg = req.l10n(MSG_STRINGS.DB_ERROR)
                    SysNotifyModel.notifyAdmin err?.toString()+clientMsg
                    return respError {
                      clientMsg,
                      resp,
                      sysErr:err}
                  # return respError {msg:req.l10n(MSG_STRINGS.DB_ERROR), resp},err if err
              if post.wpHosts
                len = post.cmnts?.length
                addWpComment post._id, cmnt, len-1, post.wpHosts
          
          # 尝试使用LLM审核，失败时回退到原有审核方式
          try
            # 获取评论过滤提示词模版
            promptTemplates = await PromptsModel.getCachedPromptTemplates('comment_filter')
          catch error
            debug.error "获取提示词模版异常，回退到原有审核:", error
            # 异常情况下回退到原有审核方式
            checkTextContent checkContentObj, (err, ret) ->
              handleModerationResult(err, ret)
          # 使用LLM审核（如果有模版）
          if promptTemplates.length > 0
            checkTextContent checkContentObj, promptTemplates, (err, ret) ->
              if err
                debug.error "LLM评论审核失败，回退到原有审核:", err
                # 回退到原有审核方式
                checkTextContent checkContentObj, (err, ret) ->
                  handleModerationResult(err, ret)
              else
                handleModerationResult(err, ret)
          else
            # 没有模版，使用原有审核方式
            checkTextContent checkContentObj, (err, ret) ->
              handleModerationResult(err, ret)
      else if action is 'sticky'
        unless groupAdmin or UserModel.accessAllowed 'forumAdmin',user
          return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
        unless typeof sticky is 'boolean' and helpers.isNumber index
          return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
        ForumModel.stickyComment {id,sticky,gid,index},(err,ret)->
          if err
            clientMsg = req.l10n(err)
            SysNotifyModel.notifyAdmin err?.toString()+clientMsg
            return respError {
              clientMsg,
              resp,
              sysErr:err}
          #return respError req.l10n('Sticky comments failed'), resp unless ret.lastErrorObject.n
          return resp.send {ok:1, cmntl: ret.cmntl}
      else if action is 'tup'
        unless typeof tup is 'boolean' and helpers.isNumber index
          return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
        ForumModel.tupComment {id,gid,tup,index,uid:user._id},(err,ret)->
          return respError {clientMsg:req.l10n(err), resp} if err
          return resp.send {ok:1}
      else
        console.error "Forum Unknown Action: #{action}"
        return respError {clientMsg:"Unknown Action",resp,category:"Unknown Action from forum comments"}
