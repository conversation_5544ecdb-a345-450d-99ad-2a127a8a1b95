<link rel="stylesheet" href="/css/apps/deals.css">
<div class="filter">
  <div id="schoolQuickFilter" class="bar bar-standard bar-header-secondary">
    <div @click="addFilter('date')">
      {{ $_(date ||  'Date')}}
      <span class="icon fa fa-caret-down" :class="{'fa-caret-up':showBackdrop && (waitFilter == 'date')}"></span>
    </div>
    <div @click="addFilter('tp')">
      {{ $_(tp ||  'Type')}}
      <span class="icon fa fa-caret-down" :class="{'fa-caret-up':showBackdrop && (waitFilter == 'tp')}"></span>
    </div>
    <div @click="addFilter('side')">
      {{ $_(side ||  'Role')}}
      <span class="icon fa fa-caret-down" :class="{'fa-caret-up':showBackdrop && (waitFilter == 'side')}"></span>
    </div>
    <div @click="addFilter('agent')" v-if="dispVar.isClaimAdmin"> {{- Agent}}
      <span class="icon fa fa-caret-down" :class="{'fa-caret-up':showBackdrop && (waitFilter == 'agent')}"></span>
    </div>
  </div>
  <div class="total">
    <span class="count">{{ cntTotal}} <span>{{- Deals}}</span> </span>
    <span class="left">
      <span class="clnt" v-if='agentInfo.fnm'>
        <span class="name">{{ agentInfo.fnm}}</span>
        <a class="icon icon-close remove" @click="deleteClntFilter()"></a>
      </span>
    </span>
    <span class="sort">
      <span :class="{select:sort == 'tsNum'}" @click="selectSort('tsNum')">
        {{- Claim Date}}
      </span>
      <span :class="{select:sort == 'sldd'}" @click="selectSort('sldd')">
        {{- Sold Date}}
      </span>
    </span>
  </div>
  <div class="filterList table-view" v-show="filterList">
    <div class="table-view-cell" v-for="f in filterList" @click="selectFilter(f)">{{ $_(f)}}</div>
  </div>
  <div class="filterList table-view" v-if="showAgents">
    <div class="table-view-cell agent-info" v-for="u in users" @click="selectAgent(u)">
      <img :src="u.avt || '/img/logo.png'" @error="u.avt = '/img/logo.png'" referrerpolicy="same-origin">
      <span class="info">{{ u.fnm}}</span>
    </div>
  </div>
</div>
<div class="backdrop" :class='{show:showBackdrop}' @click='reset()'></div>
<div class='prop-list-container'>
  <div class="prop-list prop-claim" v-if="list.length">
    <div class="claim-list" v-for="(prop,idx) in list" >
      <div class="date-block" v-show="prop.showDate">
        <span class="circle"></span>
        <span class="date">{{prop.dateString}}</span>
      </div>
      <div class="agent-info" v-show="prop.user">
        <img :src="prop.user.avt || '/img/logo.png'" @error="prop.user.avt = '/img/logo.png'" referrerpolicy="same-origin">
        <div class="info">
          <div class="name">{{ prop.user.fnm}}</div>
          <div class="other-info">
            <span>{{ prop.claimMt}}</span>
            <span v-if="prop.side" style="text-transform: capitalize;">&#183; {{ $_(prop.side)}}</span>
          </div>
        </div>
        <div class="edit-btn">
          <span class="btn btn-nooutline pull-right" style="padding:0px;margin-top:-3px;" v-if="prop.showDelete && dispVar.isClaimAdmin">
            <span class="pull-right btn btn-nooutline" style="border:1px solid #fff;" @click.stop.prevent="prop.showDelete = false">{{- Cancel}}</span>
            <span class="pull-right btn btn-negative" @click.stop.prevent="confirmDelete(prop,idx)">{{- Delete}}</span>
          </span>
          <span v-if="!prop.showDelete && dispVar.isClaimAdmin">
            <span class="sprite16-18 sprite16-4-8" style="color:rgba(17,17,17,.6)" @click="prop.showDelete = true"></span>
          </span>
        </div>
      </div>
      <!-- Deal failed 提示 -->
      <div v-if="prop.prop && prop.prop.isFailed" class="deal-failed-notice">
        <span class="fa fa-exclamation-circle"></span> {{- Deal failed}}
      </div>
      <div class="stakeholders" v-show="prop.people.length">
        <div v-for="s in prop.people">
          <span class="stakeholders-role"> [{{ $_(s.role) || '?' }}] </span>
          <span v-if="s.nm">{{ s.nm}}</span>
          <span> &#183; {{ $_(s.source)}}</span>
          <span> &#183; {{ s.pcOfShr}}%</span>
        </div>
      </div>
      <div class="addClient" v-if="prop.claimedUser.length">
        <div class="images" :style="{width:((20*prop.claimedUser.length)+5)+'px'}">
          <img v-for="(u,i) in prop.claimedUser" :style="{left: 10*i+'px','z-index':prop.claimedUser.length-i}" :src="u.avt || '/img/logo.png'" @error="u.avt = '/img/logo.png'" referrerpolicy="same-origin" />
        </div>
        <div class="size12 claimed">
          <span>{{ $_("Claimed by")}}&nbsp;</span>
          <span v-for="(u,i) in prop.claimedUser">{{ u.fnm}}({{ u.pcOfShr}}%)&nbsp;</span>
        </div>
      </div>
      <div class="stakeholders" v-if="prop.m"> {{ prop.m}}</div>
      <div class="propItem">
        <prop-item :prop="prop.prop" :lang="'en'" ></prop-item>
      </div>
    </div>
  </div>
</div>
<prop-fav-actions v-bind:disp-var='dispVar'></prop-fav-actions>

<div style="display:none">
  <span v-for="(v,k) in strings">{{ $_(v[0], v[1])}}</span>
</div>
