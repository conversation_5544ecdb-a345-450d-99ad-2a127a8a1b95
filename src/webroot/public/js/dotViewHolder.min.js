"use strict";function _toArray(e){return _arrayWithHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,o=Array(t);r<t;r++)o[r]=e[r];return o}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithHoles(e){if(Array.isArray(e))return e}var appendDiv,backdrop,bodyWidth,changeSelModel,changelanguage,compareTableSet,compressionRatio,dataUrl,displaySharing,downloadPic,dragEnd,genImg,hasSort,imageFormat,img,imgCanvas,imgs,j,len,loaded,openPrint,origProps,readyCount,ref,saveSelfContent,shareClicked,shareFriend,shareTimeline,showSignature,sortCancel,sortClicked,sortConfirm,stylePage,tableWidth,updateTextareaAtr,url;if(imgCanvas=null,dataUrl=null,bodyWidth=null,imageFormat="image/jpeg",compressionRatio=.8,url=window.location.href,hasSort=!0,stylePage=null,imgs=document.querySelectorAll("img"),readyCount=0,"undefined"!=typeof vars&&null!==vars?vars.props:void 0){var _vars$props=_toArray(vars.props);origProps=_vars$props.slice(0)}if(shareClicked=function(e){return toggleModal("shareModal","open"),backdrop.style.display="block"},displaySharing=function(){return document.querySelector(".footer-tab").style.display="block",document.querySelector("#shareModal").style.display="block"},updateTextareaAtr=function(e){var t,r,o,n,a;for(a=[],o=0,n=(t=document.getElementsByClassName("iconEdit")).length;o<n;o++)r=t[o],a.push(r.setAttribute("icon",e));return a},compareTableSet=function(){return updateTextareaAtr(""),bodyWidth=document.querySelector("#compareTable").clientWidth+"px",document.querySelector("#compareTable").style.width=document.querySelector("#tableInfo").clientWidth+40+"px",document.querySelector("#compareTable").style.paddingBottom="25px"},genImg=function(){var e,t;return e={proxy:"/1.5/htmltoimg/imgflyer",useCORS:!1},t=document.body,document.querySelector("#compareTable")&&(compareTableSet(),t=document.querySelector("#compareTable"),e.scale=.8,window.vars&&vars.supportPng&&(imageFormat="image/png",compressionRatio=.85)),e.height=document.querySelector("#dotRenderHTMLWrapperRM").clientHeight||667,html2canvas(t,e).then((function(e){dataUrl=(imgCanvas=e).toDataURL(imageFormat,compressionRatio),document.querySelector("#share-image").innerHTML=dataUrl,displaySharing(),shareClicked(),document.querySelector("#compareTable")&&(document.querySelector("#compareTable").style.width=bodyWidth,document.querySelector("#compareTable").style.paddingBottom="50px",updateTextareaAtr(""))}))},loaded=function(){if(++readyCount>=imgs.length)return setTimeout((function(){return genImg()}),500)},showSignature=function(){var e,t,r,o;for(e=document.getElementById("sign").checked,t=0,r=(o=document.querySelectorAll(".signature")).length;t<r;t++)o[t].style.display=e?"block":"none";return url=url.replace(/showSign=(true|false)/,"showSign="+e)},changeSelModel=function(e){return url=url.replace(/selModel=(diff|list)/,"selModel="+e),url="".concat(url,"&cusContent=true"),window.location=url},document.querySelector("#compareTable"))vars.showPrint?(document.querySelector("#printBtn").style.display="block",document.querySelector("#shareBtn").style.display="none"):(document.querySelector("#id_with_sign").style.display="block",document.querySelector("#selLang").style.display="block",document.querySelector("#selModel").style.display="block",document.querySelector("#sortBtn").style.display="block",tableWidth=document.querySelector("#tableInfo").clientWidth,document.querySelector("#footer").style.width=tableWidth+"px","zh-cn"===(ref=vars.locLang)||"zh"===ref?document.querySelector(".lang_zh").style.backgroundColor="#CCC":"en"===vars.locLang&&(document.querySelector(".lang_en").style.backgroundColor="#CCC"),"list"===vars.selModel?document.querySelector(".model_list").style.backgroundColor="#CCC":document.querySelector(".model_diff").style.backgroundColor="#CCC"),"true"===vars.showSign?document.getElementById("sign").checked=!0:document.getElementById("sign").checked=!1,showSignature(),displaySharing();else for(document.querySelector(".footerDisplay").style.justifyContent="flex-end",j=0,len=imgs.length;j<len;j++)(img=imgs[j]).complete?loaded():(img.addEventListener("load",loaded),img.addEventListener("error",(function(e){loaded(),console.error(e.toString())})));shareFriend=function(){return RMSrv.share("wechat-cust",null,{type:"imageUrl"})},shareTimeline=function(){return RMSrv.share("wechat-cust",null,{tp:"timeline",type:"imageUrl"})},downloadPic=function(){return dataUrl?RMSrv.downloadImage(dataUrl,{},(function(e,t){var r;return r=e||t,RMSrv.dialogAlert(r)})):RMSrv.dialogAlert("Not Generated!")},backdrop=document.querySelector(".backdrop"),changelanguage=function(e){return url=url.replace(/=(zh-cn|en|zh|kr)/,"="+e),window.location=url},document.querySelector("#shareBtn").addEventListener("click",genImg),document.querySelector("#cancelBtn").addEventListener("click",(function(e){return document.querySelector("#compareTable")&&(document.querySelector("#compareTable").style.width=bodyWidth,document.querySelector("#compareTable").style.paddingBottom="50px",updateTextareaAtr("")),toggleModal("shareModal","close"),backdrop.style.display="none"})),openPrint=function(){var e,t,r,o;return updateTextareaAtr(""),document.querySelector(".footer-tab").style.display="none",t=document.querySelector("#tableInfo").clientWidth,/Apple Computer/.test(navigator.vendor)?e=.3*t:(e=.27*t,(stylePage=document.createElement("style")).innerHTML="@page { margin: 0; }",document.querySelector(".page-container").appendChild(stylePage)),vars.props.length>=5?(r=Math.floor(279/e*100)/100,document.getElementsByTagName("body")[0].style.zoom=r):vars.props.length>2&&(o=Math.floor(216/e*100)/100,document.getElementsByTagName("body")[0].style.zoom=o),window.print()},addEventListener("afterprint",(function(e){return updateTextareaAtr(""),document.getElementsByTagName("body")[0].style.zoom=1,stylePage&&document.querySelector(".page-container").removeChild(stylePage),stylePage=null,document.querySelector(".footer-tab").style.display="block"})),dragEnd=function(e){var t,r,o,n,a,l;return o=document.querySelector("#sortPreview"),a=e.newIndex,l=e.oldIndex,r=o.children[l],a>=o.children.length&&(a=o.children.length-1),t=o.children[a],o.removeChild(t),a>l?o.insertBefore(t,r):o.insertBefore(t,r.nextSibling),n=vars.props.splice(l,1),vars.props.splice(a,0,n[0]),appendDiv()},sortConfirm=function(){var e,t,r,o,n;for(e=[],r=0,o=(n=vars.props).length;r<o;r++)t=n[r],e.push(t._id);return e=e.join(),url="".concat(location.href.split("?")[0],"?lang=").concat(vars.locLang,"&showSign=").concat(vars.showSign,"&nm=comparetable&ids=").concat(e,"&cusContent=true&selModel=").concat(vars.selModel),hasSort=!0,window.location=url},sortCancel=function(){return hasSort=!1,document.querySelector("#sortModal").style.display="none",backdrop.style.display="none"},saveSelfContent=function(e){var t,r,o,n,a,l,i,c,d;for(r=[],d=document.getElementsByClassName("sizeClass"),t=document.getElementsByClassName("bltYrClass"),n=l=0,i=(c=document.getElementsByClassName("remarkClass")).length;l<i;n=++l)a=c[n],o={id:origProps[n]._id},a.innerText.length&&(o.remark=a.innerText),(null!=t?t[n].innerText.length:void 0)&&(o.bltYr=t[n].innerText),(null!=d?d[n].innerText.length:void 0)&&(o.size=d[n].innerText),r.push(o);return fetchData("/1.5/saveCMA/saveCustomContent",{body:{content:r}},(function(t,r){var o;return(null!=r?r.ok:void 0)?e?changeSelModel(e):sortConfirm():(o=t||(null!=r?r.e:void 0)||"CMA saved faild, please try again",RMSrv.dialogAlert(o))}))},appendDiv=function(){var e,t,r,o,n,a,l;for(document.getElementById("sortPreview").innerHTML="",l=[],r=0,o=(a=vars.props).length;r<o;r++)t=a[r],n=document.createElement("div"),e="<div class='propInfo'><span style='position:relative;'><img src='".concat(t.thumbUrl||"/img/noPic.png","' style='width:100%;background-image:url(&quot;/img/noPic.png&quot;);height:71px;'> <span class='fa fa-arrows' style='position:absolute;left:45%;top:-35px;color:#fff;font-size:15px;'></span></span><p>").concat(t.lpValStrRed||t.spValStrRed,"</p><div><p>").concat(t.addr||"","</p><p>").concat(t.onD||"","</p></div></div>"),n.innerHTML=e,l.push(document.getElementById("sortPreview").appendChild(n));return l},sortClicked=function(){var e,t;return hasSort&&(appendDiv(),e={animation:150,ghostClass:"sort-placeholder",delay:100,delayOnTouchOnly:!0,touchStartThreshold:10,onUpdate:function(e){return dragEnd(e)}},t=document.querySelector("#sortPreview"),new Sortable(t,e)),document.querySelector("#sortModal").style.display="block",backdrop.style.display="block"};
