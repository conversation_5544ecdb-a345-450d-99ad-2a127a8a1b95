"use strict";function _typeof(t){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof(t)}function _regeneratorRuntime(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_regeneratorRuntime=function(){return e};var t,e={},r=Object.prototype,a=r.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",s=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function d(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,r){return t[e]=r}}function c(t,e,r,a){var o=e&&e.prototype instanceof v?e:v,i=Object.create(o.prototype),s=new L(a||[]);return n(i,"_invoke",{value:C(t,r,s)}),i}function u(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=c;var p="suspendedStart",m="suspendedYield",h="executing",g="completed",f={};function v(){}function w(){}function y(){}var b={};d(b,i,(function(){return this}));var _=Object.getPrototypeOf,$=_&&_(_(I([])));$&&$!==r&&a.call($,i)&&(b=$);var S=y.prototype=v.prototype=Object.create(b);function k(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function r(n,o,i,s){var l=u(t[n],t,o);if("throw"!==l.type){var d=l.arg,c=d.value;return c&&"object"==_typeof(c)&&a.call(c,"__await")?e.resolve(c.__await).then((function(t){r("next",t,i,s)}),(function(t){r("throw",t,i,s)})):e.resolve(c).then((function(t){d.value=t,i(d)}),(function(t){return r("throw",t,i,s)}))}s(l.arg)}var o;n(this,"_invoke",{value:function(t,a){function n(){return new e((function(e,n){r(t,a,e,n)}))}return o=o?o.then(n,n):n()}})}function C(e,r,a){var n=p;return function(o,i){if(n===h)throw Error("Generator is already running");if(n===g){if("throw"===o)throw i;return{value:t,done:!0}}for(a.method=o,a.arg=i;;){var s=a.delegate;if(s){var l=M(s,a);if(l){if(l===f)continue;return l}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(n===p)throw n=g,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);n=h;var d=u(e,r,a);if("normal"===d.type){if(n=a.done?g:m,d.arg===f)continue;return{value:d.arg,done:a.done}}"throw"===d.type&&(n=g,a.method="throw",a.arg=d.arg)}}}function M(e,r){var a=r.method,n=e.iterator[a];if(n===t)return r.delegate=null,"throw"===a&&e.iterator.return&&(r.method="return",r.arg=t,M(e,r),"throw"===r.method)||"return"!==a&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+a+"' method")),f;var o=u(n,e.iterator,r.arg);if("throw"===o.type)return r.method="throw",r.arg=o.arg,r.delegate=null,f;var i=o.arg;return i?i.done?(r[e.resultName]=i.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,f):i:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,f)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function D(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function L(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function I(e){if(e||""===e){var r=e[i];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,o=function r(){for(;++n<e.length;)if(a.call(e,n))return r.value=e[n],r.done=!1,r;return r.value=t,r.done=!0,r};return o.next=o}}throw new TypeError(_typeof(e)+" is not iterable")}return w.prototype=y,n(S,"constructor",{value:y,configurable:!0}),n(y,"constructor",{value:w,configurable:!0}),w.displayName=d(y,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,d(t,l,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},k(x.prototype),d(x.prototype,s,(function(){return this})),e.AsyncIterator=x,e.async=function(t,r,a,n,o){void 0===o&&(o=Promise);var i=new x(c(t,r,a,n),o);return e.isGeneratorFunction(r)?i:i.next().then((function(t){return t.done?t.value:i.next()}))},k(S),d(S,l,"Generator"),d(S,i,(function(){return this})),d(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var a in e)r.push(a);return r.reverse(),function t(){for(;r.length;){var a=r.pop();if(a in e)return t.value=a,t.done=!1,t}return t.done=!0,t}},e.values=I,L.prototype={constructor:L,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(D),!e)for(var r in this)"t"===r.charAt(0)&&a.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function n(a,n){return s.type="throw",s.arg=e,r.next=a,n&&(r.method="next",r.arg=t),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],s=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var l=a.call(i,"catchLoc"),d=a.call(i,"finallyLoc");if(l&&d){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!d)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&a.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc&&(o=null);var i=o?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,f):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),D(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var a=r.completion;if("throw"===a.type){var n=a.arg;D(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,r,a){return this.delegate={iterator:I(e),resultName:r,nextLoc:a},"next"===this.method&&(this.arg=t),f}},e}function asyncGeneratorStep(t,e,r,a,n,o,i){try{var s=t[o](i),l=s.value}catch(t){return void r(t)}s.done?e(l):Promise.resolve(l).then(a,n)}function _asyncToGenerator(t){return function(){var e=this,r=arguments;return new Promise((function(a,n){var o=t.apply(e,r);function i(t){asyncGeneratorStep(o,a,n,i,s,"next",t)}function s(t){asyncGeneratorStep(o,a,n,i,s,"throw",t)}i(void 0)}))}}var dataURItoBlob,getCanvasImage,getS3Config,imageToDataUri,initSummernote,init_map,processFiles,readFile,splitName,uploadFile,indexOf=[].indexOf;initSummernote=function(t){var e,r;return e=$(".summernote"),r=[["del",["delToStart","delToEnd"]],["color",["color"]],["para",["ul","ol","paragraph"]],["fontsize",["fontsize"]],["upLoadImg",["select"]],["insert",["hr","link"]],["style",["style","bold","italic","underline","clear"]],["misc",["undo","redo","codeview"]]],t&&r.splice(4,1),e.summernote({toolbar:r,onFocus:function(){var t;if(window.keyboard&&window.isIOS)return t=parseInt(window.keyboardHeight||0),$("#editorModal .note-editable.panel-body").css("padding-bottom",t+"px")},onBlur:function(){if(window.keyboard&&window.isIOS&&$("#editorModal .note-editable.panel-body").css("padding-bottom","0px"),!window.onEditImgSelect)return e.summernote("saveRange")}})},processFiles=function(t){var e,r;return e=void 0,r=void 0,t&&"undefined"!=typeof FileReader?(r=0,(e=function(){var a;if(a=void 0,r<t.length)return a=t[r++],readFile(a,(function(){return e()}))})()):RMSrv.dialogAlert("Unsuppored browser. Can't process files.")},getS3Config=function(t,e,r){var a;return(a={}).ext="jpg",a.w=t.width,a.h=t.height,a.s=t.size,a.t=r?1:0,$("#loading-bar-spinner").css("display","block"),$.ajax({url:amGloble.getS3Config,data:a,type:"post"}).done((function(t){t.key?($("#loading-bar-spinner").css("display","none"),window.s3config=t,e&&e()):flashMessage("server-error")})).fail((function(){flashMessage("server-error")}))},uploadFile=function(t,e){var r,a,n,o,i,s;return a=void 0,n=void 0,o=void 0,i=void 0,e?(a=t.blob2,n=window.s3config.thumbKey,o=window.s3config.thumbPolicy,i=window.s3config.thumbSignature):(a=t.blob,n=window.s3config.key,o=window.s3config.policy,i=window.s3config.signature),(r=new FormData).append("acl","public-read"),r.append("key",n),r.append("Content-Type",window.s3config.contentType),r.append("policy",o),r.append("x-amz-credential",window.s3config.credential),r.append("x-amz-date",window.s3config.date),r.append("x-amz-signature",i),r.append("x-amz-algorithm","AWS4-HMAC-SHA256"),r.append("file",a,n),s=0,$("#loading-bar-spinner").css("display","block"),$.ajax({url:amGloble.savePicS3,data:r,type:"post",processData:!1,contentType:!1,crossDomain:!0,headers:{Origin:"anonymous","Access-Control-Request-Origin":"anonymous"}}).done((function(){return s=1})).always((function(t){var r;if(s||204===(null!=t?t.status:void 0)){if($("#loading-bar-spinner").css("display","none"),window.isThumbImg)return r="http://"+window.s3config.s3bucket+"/"+window.s3config.key,window.cardMeta.img=r,setTimeout((function(){$("#thumbImg").attr("src",r)}),500),void(window.isThumbImg=!1);window.isFlyer(amGloble.type,window.shSty)?(e||(window.ctClone.bg="http://"+window.s3config.s3bucket+"/"+window.s3config.key),window.setPreviewContents(window.ctClone)):e||$(".summernote").summernote("insertImage","http://"+window.s3config.s3bucket+"/"+window.s3config.key,window.s3config.key)}else flashMessage("server-error"),RMSrv.fetch(amGloble.uploadFail,{method:"POST",body:{}})}))},readFile=function(t,e){var r;return r=void 0,/image/i.test(t.type)?((r=new FileReader).onload=function(r){return $("<img/>").load((function(){var r,a;return void 0,a=getCanvasImage(this,t),flashMessage("img-inserted"),toggleModal("imgSelectModal"),r=a.width>400||a.height>300,getS3Config(a,(function(){if(uploadFile(a,!1),r)return uploadFile(a,!0)}),r),e()})).attr("src",r.target.result)},r.readAsDataURL(t)):(RMSrv.dialogAlert(t.name+" unsupported format : "+t.type),e())},imageToDataUri=function(t,e,r){var a,n;return n=(a=document.createElement("canvas")).getContext("2d"),a.width=e,a.height=r,n.drawImage(t,0,0,e,r),a.toDataURL()},splitName=function(t,e){var r;return void 0,(r=t.lastIndexOf("."))>0?[t.substr(0,r),t.substr(r+1).toLowerCase()]:[t,"."+e.substr(e.lastIndexOf("/")).toLowerCase()]},dataURItoBlob=function(t){var e,r,a,n,o,i;for(void 0,r=void 0,void 0,n=void 0,o=void 0,void 0,r=t.split(",")[0].indexOf("base64")>=0?atob(t.split(",")[1]):unescape(t.split(",")[1]),i=t.split(",")[0].split(":")[1].split(";")[0],e=new ArrayBuffer(r.length),o=new Uint8Array(e),n=0;n<r.length;)o[n]=r.charCodeAt(n),n++;return a=new DataView(e),new Blob([a],{type:i})},getCanvasImage=function(t,e){var r,a,n,o,i,s,l,d,c,u,p,m,h;return u=128,r=void 0,a=void 0,void 0,o=void 0,i=void 0,s=void 0,l=void 0,d=void 0,void 0,void 0,m=void 0,h=void 0,d=1,(t.width>1e3||t.height>1e3)&&(m=1e3/t.width,o=1e3/t.height,d=Math.min(m,o)),t.width>=t.height&&t.height>680&&(o=680/t.height)<d&&(d=o),t.width<=t.height&&t.width>680&&(m=680/t.width)<d&&(d=m),(r=document.createElement("canvas")).width=t.width*d,r.height=t.height*d,r.getContext("2d").drawImage(t,0,0,t.width,t.height,0,0,r.width,r.height),c=splitName(e.name,e.type),(i={name:e.name,nm:c[0],ext:c[1],origType:e.type,origSize:e.size,width:r.width,height:r.height,ratio:d}).type="image/jpeg",i.url=r.toDataURL(i.type,.8),i.blob=dataURItoBlob(i.url),i.size=i.blob.size,i.canvas=r,(a=document.createElement("canvas")).width=p=Math.min(128,t.width),a.height=n=Math.min(u,t.height),t.width*n>t.height*p?(h=(t.width-t.height/n*p)/2,l=t.width-2*h,s=t.height):(h=0,l=t.width,s=t.width),a.getContext("2d").drawImage(t,h,0,l,s,0,0,p,n),i.url2=a.toDataURL(i.type,.7),i.blob2=dataURItoBlob(i.url2),i.size2=i.blob2.size,i.canvas2=a,i},init_map=function(){var t,e,r,a,n;if(r=void 0,a=void 0,void 0,e=void 0,t=$("#meta-addr").val()||"Mississauga, ON, Canada",n={zoom:12,center:new google.maps.LatLng(43.7182412,-79.378058),mapTypeControl:!0,mapTypeControlOptions:{style:google.maps.MapTypeControlStyle.DROPDOWN_MENU},navigationControl:!0,mapTypeId:google.maps.MapTypeId.ROADMAP},r=new google.maps.Map(document.getElementById("id_d_map"),n),window.map=r,e=new google.maps.Geocoder)return e.geocode({address:t},(function(n,o){var i;return i=function(t){return e.geocode({latLng:t},(function(t){return t&&t.length>0?$("#housecard-page-edit-body").find("[data-role=meta-addr]").val(t[0].formatted_address):console.log("Cannot determine address at this location.")}))},o===google.maps.GeocoderStatus.OK?o!==google.maps.GeocoderStatus.ZERO_RESULTS?(r.setCenter(n[0].geometry.location),new google.maps.InfoWindow({content:"<b>"+t+"</b>",size:new google.maps.Size(150,50)}),(a=new google.maps.Marker({position:n[0].geometry.location,map:r,draggable:!0,animation:google.maps.Animation.DROP,title:t,optimized:!1})).addListener("click",(function(){return null!==a.getAnimation()?a.setAnimation(null):a.setAnimation(google.maps.Animation.BOUNCE)})),google.maps.event.addListener(a,"dragend",(function(){return i(a.getPosition())}))):RMSrv.dialogAlert("No results found"):RMSrv.dialogAlert("Geocode was not successful for the following reason: "+o)}))},$((function(){var t,e;return window._id=null,t={id:"housecard-page-edit-body",init:function(){return this._doms={},this._datas={withDl:!0,withSign:!0},this.initDoms(),this.bindEvents(),this.getData(),window.isFlyer=this.isFlyer},initDoms:function(){var t;return t=this.$=$("#"+this.id),$.extend(this._doms,{metaTitle:t.find("[data-role=meta-title]"),metaEditor:t.find("[data-role=meta-editor]"),metaTemplate:t.find("[data-role=meta-template]"),metaDesc:t.find("[data-role=meta-desc]"),metaVc:t.find("[data-role=meta-vc]"),metaAddr:t.find("[data-role=meta-addr]"),cpName:t.find("[data-role=company-name]"),nkPhoto:t.find("[data-role=nick-photo]"),nki:t.find("[data-role=nick-img]"),nknm:t.find("[data-role=nick-nm]"),intr:t.find("[data-role=intr]"),mapUl:t.find("[data-role=map-item-ul]"),ctct:t.find("[data-role=ctct]"),ctctWx:t.find("[data-role=ctct-wx]"),ctctWxQr:t.find("[data-role=ctct-wxqr]"),ctctWxQrW:t.find("[data-role=ctct-wxqr-wrapper]"),ctctGrpQrcd:t.find("[data-role=ctct-grpqrcd]"),ctctGrpQrcdW:t.find("[data-role=ctct-grpqrcd-wrapper]"),ctctTel:t.find("[data-role=ctct-tel]"),ctctTel2:t.find("[data-role=ctct-tel2]"),ctctEml:t.find("[data-role=ctct-eml]"),ctctWeb:t.find("[data-role=ctct-web]"),ctctCpny:t.find("[data-role=ctct-cpny]"),ctctCpny_pstn:t.find("[data-role=ctct-cpny_pstn]"),cpnydtl:t.find("[data-role=cpnydtl]"),cpnydtlFax:t.find("[data-role=cpnydtl-fax]"),cpnydtlTel:t.find("[data-role=cpnydtl-tel]"),cpnydtlTel2:t.find("[data-role=cpnydtl-tel2]"),cpnydtlAds:t.find("[data-role=cpnydtl-ads]"),cpnydtlWeb:t.find("[data-role=cpnydtl-web]"),medLink:t.find("[data-role=media-link]"),propDetailPane:t.find("[data-role=prop-detail-pane]"),propImgPane:t.find("[data-role=prop-img-pane]"),propPrice:t.find("[data-role=prop-lp_price]"),propType:t.find("[data-role=prop-type_own1_out]"),propBr:t.find("[data-role=prop-br]"),propKit:t.find("[data-role=prop-num_kit]"),propPak:t.find("[data-role=prop-gar_spaces]"),propBsmt:t.find("[data-role=prop-bsmt1_out]"),propBath:t.find("[data-role=prop-bath_tot]"),propLot:t.find("[data-role=prop-front_ft]"),propExt:t.find("[data-role=prop-constr1_out]"),propTax:t.find("[data-role=prop-taxes]"),propSqft:t.find("[data-role=prop-sqft]"),propAC:t.find("[data-role=prop-a_c]"),propCVC:t.find("[data-role=prop-central_vac]"),propAge:t.find("[data-role=prop-yr_built]"),propPool:t.find("[data-role=prop-pool]"),propFuel:t.find("[data-role=prop-fuel]"),propRltr:t.find("[data-role=prop-rltr]"),propRemark:t.find("[data-role=prop-ad_text]"),topic:t.find("[data-role=topic]"),topicContent:t.find("[data-role=topic-content]")})},setShareUrl:function(){var t,e,r;if(r=this,t=amGloble.host+"/1.5/wecard/prop/"+amGloble.id+"/"+window._id,e="tp=wecard&uid="+amGloble.id+"&id="+window._id,r._datas.withDl?(t+="?wDl=1",e+="&dl=1",r._datas.withSign&&(t+="&sgn=1")):r._datas.withSign&&(t+="?sgn=1"),r._datas.withSign&&(e+="&sgn=1"),amGloble.lang&&(e+="&lang="+amGloble.lang),window._id&&$("#share-url").text(t),window._id)return $("#share-data").text(e)},bindEvents:function(){var t;return t=this,/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|CFNetwork/i.test(navigator.userAgent.toLowerCase())&&(window.isIOS=RMSrv.isIOS(),RMSrv.getKeyboard((function(t){return window.keyboard=t,window.keyboard.disableScroll(!1)}))),this.$.on("click",".footer-icon-music",(function(){return $(".bgs-mp3").toggle(),"none"===$(".bgs-map").css("display")&&$("#backdrop").toggle(),$(".bgs-map").hide(),t.getMusicLinks(),!1})).on("click","#li-music-close",(function(){var e;return(e=document.getElementById(t.adid))&&(e.pause(),t.adid=null),$(".bgs-map").hide(),$(".bgs-mp3").toggle(),$("#backdrop").toggle(),!1})).on("click","a.btn-sort",(function(e){var r,a,n,o,i,s,l,d,c;return s=e.currentTarget,r=(a=$(s).parents("li")).clone(),n=a.prev(),i=a.index(),l=n.index(),n&&n.length>0&&n.is("li")&&i>0&&((c=t._datas.pageData.card.seq)&&c.length>0&&(o=c[i],d=c[l],c[l]=o,c[i]=d,t._datas.pageData.card.seq=c),n.before(r),a.remove()),!1})).on("click","#thumbImg",(function(){var e;return e=t._datas.pageData,console.log(t),window.isThumbImg=!0,window.cardMeta=e.card.meta,toggleModal("imgSelectModal")})).on("click","#listPageBtn",(function(){if(window.keyboard)return window.keyboard.disableScroll(!1)})).on("click","#showMap",(function(){var t,e,r,a;return window.mapLoaded?(r=window.map,t=$("#meta-addr").val()||"Mississauga, ON, Canada",(e=new google.maps.Geocoder)&&e.geocode({address:t},(function(e,a){var n,o;return a===google.maps.GeocoderStatus.OK?a!==google.maps.GeocoderStatus.ZERO_RESULTS?(r.setCenter(e[0].geometry.location),n=new google.maps.InfoWindow({content:"<b>"+t+"</b>",size:new google.maps.Size(150,50)}),o=new google.maps.Marker({position:e[0].geometry.location,map:r,title:t,optimized:!1}),google.maps.event.addListener(o,"click",(function(){return n.open(r,o)}))):RMSrv.dialogAlert("No results found"):RMSrv.dialogAlert("Geocode was not successful for the following reason: "+a)}))):($("#id_map_outer").css("display","block"),window.mapLoaded=!0,(a=document.createElement("script")).type="text/javascript",a.src=window.gurl+"&callback=init_map",document.body.appendChild(a)),!1})).on("click",".thumb-wrapper img",(function(){var e,r;return $(this).toggleClass("selected"),void 0,e=$(this).prop("alt"),(r=t._datas.userData.selected.indexOf(e))>=0?t._datas.userData.selected.splice(r,1):t._datas.userData.selected.push(e)})).on("click","#toggleImgSelect",(function(){return window.keyboard&&window.keyboard.isVisible&&(window.keyboard.close(),window.onEditImgSelect=!1),window.isThumbImg&&(window.isThumbImg=!1),toggleModal("imgSelectModal")})).on("click","#listUserPics",_asyncToGenerator(_regeneratorRuntime().mark((function e(){var r,a,n,o,i;return _regeneratorRuntime().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return $("#imgSelectPicList").html(""),e.prev=1,e.next=4,RMSrv.fetch(amGloble.getUserFiles,{method:"GET"});case 4:if(1===(i=e.sent).ok)for(n in t._datas.userData=i,null!=(o=t._datas.userData)&&(o.selected=[]),i.pl)r=i.base+"/"+(null!=i.pl[n].tA?i.pl[n].tA:i.pl[n].nm),a="<span class='thumb-wrapper'><img src='"+r+"' alt='"+n+"'> </span>",$("#imgSelectPicList").append(a);else console.log("Error has happened while getting file list!");return e.abrupt("return");case 9:return e.prev=9,e.t0=e.catch(1),e.t0,flashMessage("server-error"),e.abrupt("return");case 14:return e.abrupt("return",!0);case 15:case"end":return e.stop()}}),e,null,[[1,9]])})))).on("click","#saveFrame",(function(e){var r,a,n,o,i,s;return r=$(".summernote").summernote("code"),t.isFlyer(t._datas.pageData.card.tp,null!=(i=t._datas.pageData.card.meta)?i.shSty:void 0)&&t._datas.ctClone?(t._datas.ctClone.m=r,t.setPreviewContents(t._datas.ctClone)):(a=e.currentTarget,$(a).parents("li"),s=t._datas.pageData.card.seq,$(s[t.editIndex].m),o=t.editIndex+1,s[t.editIndex].m=r,n=$("#edit-page-contents-ul > li:nth-of-type("+o+")"),$(r).length>1&&(r=$("<div>").html(r)[0].outerHTML),r+=t._doms.ctrlButtons,n.html(r)),t._doms.metaDesc.val()||t._doms.metaDesc.val($(r).text().replace(/\s|\n|\r|\v/g,"").substr(0,50)),window.keyboard&&(window.keyboard.disableScroll(!1),window.keyboard.isVisible&&window.keyboard.close()),$(".summernote").summernote("destroy"),initSummernote(),!1})).on("click","#savePicFrame",(function(){var e,r,a,n,o,i;for(a in o=t._datas.pageData.card.seq,$(o[t.editIndex]),e=t.getContentFromCt(t._datas.ctClone),r=t.editIndex+1,n=t._datas.ctClone)i=n[a],o[t.editIndex][a]=i;return $("#edit-page-contents-ul > li:nth-of-type("+r+") > div:first-child").replaceWith(e),!1})).on("click touchend","#gal-del-btn",(function(t){var e,r;return r=(e=$(t.currentTarget)).parent("div"),e.hide(),$("#gal-del-yes-btn").show(),$("#gal-del-can-btn").show(),r.addClass("active"),!1})).on("click touchend","#gal-del-can-btn",(function(t){var e;return e=$(t.currentTarget).parent("div"),$("#gal-del-btn").show(),$("#gal-del-can-btn").hide(),$("#gal-del-yes-btn").hide(),e.removeClass("active"),!1})).on("click touchend","#gal-del-yes-btn",function(){var e=_asyncToGenerator(_regeneratorRuntime().mark((function e(r){var a,n,o,i,s,l,d,c;return _regeneratorRuntime().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(a=$(r.currentTarget),n=a.parent("div"),$("#gal-del-btn").show(),$("#gal-del-can-btn").hide(),$("#gal-del-yes-btn").hide(),n.removeClass("active"),(o={}).fldr=t._datas.userData.fldr,o.files=t._datas.userData.selected,o.files.length>0){e.next=11;break}return e.abrupt("return",!1);case 11:d=o.files,s=0,l=d.length;case 13:if(!(s<l)){e.next=21;break}if(i=d[s],!/^[A-Q]{1}$/.test(i.split(".")[0])){e.next=18;break}return RMSrv.dialogAlert(vars.ERR_PRO_IMG||"Cannot remove profile images!!"),e.abrupt("return",!1);case 18:s++,e.next=13;break;case 21:return e.prev=21,e.next=24,RMSrv.fetch(amGloble.deleteFiles,{method:"POST",body:o});case 24:return 1===(c=e.sent).ok?$("#listUserPics")[0].click():RMSrv.dialogAlert(c.err),e.abrupt("return");case 29:return e.prev=29,e.t0=e.catch(21),e.t0,RMSrv.dialogAlert("sever error!, please try again later"),e.abrupt("return");case 34:return e.abrupt("return",!1);case 35:case"end":return e.stop()}}),e,null,[[21,29]])})));return function(t){return e.apply(this,arguments)}}()).on("click","#saveCard",(function(e){var r;return r=t._datas.pageData,$("[data-role=meta-title]").val()?(r.card?(t.savePropCard(r.card),flashMessage("page-saved")):RMSrv.dialogAlert("Error: no card yet!"),e.preventDefault(),e.stopPropagation(),!1):(flashMessage("no-title"),!1)})).on("click","#id_with_dl",(function(){return t._datas.withDl=$(this).children("input")[0].checked,t.setShareUrl()})).on("click","#id_with_sign",(function(){return t._datas.withSign=$(this).children("input")[0].checked,t.setShareUrl()})).on("click","a.btn-delete",(function(e){var r,a,n,o;return a=e.currentTarget,r=$(a).parents("li").index(),o=t._datas.pageData.card.seq,n=$("#edit-page-contents-ul > li:nth-of-type("+(r+1)+")"),o.splice(r,1),n.remove(),!1})).on("click","a.btn-see",(function(e){var r,a,n,o,i;return o=e.currentTarget,n=(a=$(o).parents("li")).index(),i=t._datas.pageData.card.seq,(r=$(i[n].m)).toggleClass("dis"),i[n].m=r[0].outerHTML,a.toggleClass("dis"),!1})).on("click","#shareToNews",(function(){var e,r,a,n,o,i;for(o={},n=(e=t._datas.card).meta,o.wid=e._id,o.tl=e.meta.title,o.desc=e.meta.desc,o.thumb=e.meta.img,o.url=amGloble.host+"/1.5/wecard/prop/",o.logo="true",o.chnl="WePage",o.src="WePage",o.tp=e.meta.tp,o.area="Toronto",o.lang="zh-cn",o.m="",r='<div style="padding-top:20px">',r+="<h2>"+n.title+"</h2>",r+='<br><span style="margin-right:10px;">'+(n.ts?n.ts.split("T")[0]:(new Date).toDateString())+"</span>",r+='<span style="margin-right:10px;"><a onclick="'+("RMSrv.showInBrowser('"+amGloble.host+"/1.5/wesite/"+e.id+"')")+'">'+n.editor+"</a></span>",r+='<span style="margin-right:10px; color:#607fa6">'+n.custvc+"</span>",i=amGloble.emurl+encodeURIComponent(n.addr),n.addr&&(r+='<span style="color: #007aff;" class="fa fa-location-arrow" onclick="RMSrv.showInBrowser(\''+i+"')\" > </span>"),r+="</div>",o.m+=r,a=0;a<=e.seq.length-1;)o.m+=e.seq[a].m,a++;return $.ajax({url:amGloble.publishToNews,data:o,type:"post"}).done((function(t){1===t.ok?(RMSrv.share("hide"),RMSrv.dialogAlert(t.msg)):RMSrv.dialogAlert("Error has happened while publishing!")})).fail((function(){flashMessage("server-error")})),!1})).on("click","#edit-page-contents-ul > li.edit-in-summernote, #edit-page-contents-ul > li.edit-in-summernote > a.edit-in-summernote",(function(e){var r,a,n,o,i,s,l;return i=e.currentTarget,n="li"===$(i).prop("nodeName").toLowerCase()?$(i):$(i).parents("li"),t.editIndex=n.index(),a=$((null!=(s=t._datas.pageData.card.seq[t.editIndex])?s.m:void 0)||""),t._datas.ctClone=void 0,t.isFlyer(t._datas.pageData.card.tp,null!=(l=t._datas.pageData.card.meta)?l.shSty:void 0)&&"user"!==t._datas.pageData.card.seq[t.editIndex]._for?(o=t._datas.pageData.card.seq[t.editIndex],t._datas.ctClone=t.shallowCopy(o),t.setPreviewContents(t._datas.ctClone),toggleModal("frameEditorModal","open"),e.preventDefault(),e.stopPropagation()):(r=a.clone(),$(".summernote").summernote("code",$("<div>").append(r).html()),$(".summernote").summernote("destroy"),initSummernote(),toggleModal("editorModal")),window.keyboard&&window.isIOS&&window.keyboard.disableScroll(!0),!1})).on("click","#insertImage",(function(){var e,r,a,n,o,i,s;if(e=function(e,r){var a;return window.isThumbImg?(window.cardMeta.img=e,$("#thumbImg").attr("src",e),void(window.isThumbImg=!1)):t._datas.pageData.card&&t.isFlyer(t._datas.pageData.card.tp,null!=(a=t._datas.pageData.card.meta)?a.shSty:void 0)?(t._datas.ctClone.bg=e,t.setPreviewContents(t._datas.ctClone)):$(".summernote").summernote("insertImage",e,r)},r=function(){var e;return null!=(e=t._datas.userData)&&(e.selected=[]),$("#imgSelectPicList img").each((function(){return $(this).removeClass("selected")}))},$(".summernote").summernote("restoreRange"),i=$("#imgInputURL").val())e(i,i),$("#imgInputURL").val(""),toggleModal("imgSelectModal");else if(null!=t._datas.userData&&t._datas.userData.selected.length>0){for(s=(a=t._datas.userData).selected,n=0;n<=s.length-1;)e(i=a.base+"/"+a.pl[s[n]].nm,s[n]),n++;r(),toggleModal("imgSelectModal")}else if(o=$("#imgInputFiles").get(0)){if("files"in o){if(0===o.files.length)return console.log("Select one or more files."),!1;window.ctClone=t._datas.ctClone,window.setPreviewContents=t.setPreviewContents,window.getContentFromCt=t.getContentFromCt,processFiles(o.files),r(),$("#previewImg").attr("src",""),$("#imgInputFiles").val("")}}else console.log("no files");return window.onEditImgSelect=!1})).on("click","#wepageShareBtn",(function(){return t.setShareUrl()})).on("click","#wepagePreviewBtn",(function(){var t;return t=amGloble.host+"/1.5/wecard/prop/"+amGloble.id+"/"+window._id,window._id?RMSrv.showInBrowser(t):RMSrv.dialogAlert("Not Saved Yet!"),!1})).on("click","#newFrame",(function(e){var r,a,n,o,i;return r=e.currentTarget,$(r).parents("li"),i=t._datas.pageData.card.seq,n=t.isFlyer(t._datas.pageData.card.tp,null!=(o=t._datas.pageData.card.meta)?o.shSty:void 0)?'<div style="font-size: 19px;">Contents</div>':"<div><p class='text-left'>Contents</p></div>",(a={})._for="newFrame",a.m=n,i.push(a),n+=t._doms.ctrlButtons,$('<li class="edit-in-summernote">'+n+"</li>").insertBefore("li.item-add"),!1})).on("click","#pvReplaceImg",(function(){return toggleModal("imgSelectModal"),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvRemoveBgImg",(function(){return t._datas.ctClone.bg="",t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvEditPreviewText",(function(){return $(".summernote").summernote("code",t._datas.ctClone.m),$(".summernote").summernote("destroy"),initSummernote(!0),toggleModal("editorModal"),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvShiftTextPosition",(function(){return function(t){var e,r,a;return e=t.pos,a="top:10%;",r="top:45%;",t.pos=e===a?r:e===r?"bottom:10%;":a}(t._datas.ctClone),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvShiftImgPosition",(function(){return function(t){var e,r;return e=t.bgPos,r="center",t.bgPos="top"===e?r:e===r?"bottom":"top"}(t._datas.ctClone),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvTextBg",(function(){return function(t){var e,r,a;return e=t.tbg,r="background-color: rgba(0, 0, 0, 0.45);",a="background-color: rgba(0, 0, 0, 0.8);",t.tbg=e===r?a:e===a?"":r}(t._datas.ctClone),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvPreviewAnimation",(function(){return function(t){var e,r,a;return r=t.ani,(a=(e=["zoomIn","fadeIn","fadeInUp","flash","slideInUp","slideInDown","slideInLeft","slideInRight"]).indexOf(r))>=0?t.ani=e[(a+1)%e.length]:t.ani=e[0]}(t._datas.ctClone),t.setPreviewContents(t._datas.ctClone),!1})),$("#devWidthSlider").change((function(){return t.setPreviewScale($(this).val())})),$("#bgs-mp3-ul").on("click","li a",(function(){var e,r,a;return e=$(this).hasClass("anm-rotate"),r=$(this).attr("adid"),t.adid&&t.adid!==r&&($("#bgs-mp3-ul").find("[adid="+t.adid+"]").removeClass("anm-rotate"),document.getElementById(t.adid).pause(),t.adid=null),t.adid=r,a=document.getElementById(r),e?($(this).removeClass("anm-rotate"),a.pause(),t.adid=null):($(this).addClass("anm-rotate"),a.play()),!1})).on("click","li span",(function(){var e,r,a,n,o;return t.adid&&($("#bgs-mp3-ul").find("[adid="+t.adid+"]").removeClass("anm-rotate"),document.getElementById(t.adid).pause(),t.adid=null),o=(e=$(this).parents("li")).attr("urls"),n=e.attr("n"),a=e.find("a").attr("adid"),document.getElementById(a).pause(),r={url:o,nm:n},t._datas.pageData.card.music=r,$("#bgs-mp3-ul").parents(".bgs-mp3").hide(),$("#backdrop").hide(),!1}))},isFlyer:function(t,e){return"xmas1"===t||"xmas2"===t||"spring_fest"===t||"flyer"===t||("vt"===e||"vt"===amGloble.shSty)},shallowCopy:function(t){var e,r;if(null===t||"object"!==_typeof(t))return t;for(e in r=t.constructor(),t)t.hasOwnProperty(e)&&(r[e]=t[e]);return r},enableBtns:function(){return $("#wepagePreviewBtn").removeClass("disabled"),$("#wepageShareBtn").removeClass("disabled")},getUrlSrc:function(t){return/^https?:\/\/([^\/]+\.)*weixin\.qq\.com\//i.test(t)?"wechat":/^https?:\/\/([^\/]+\.)*youtube\.com\//i.test(t)?"youtube":"unknown"},getPageContent:function(t,e,r){var a;return a=t||this,RMSrv.getKeyboard((function(){var t,n,o,i,s,l,d,c,u,p,m,h;return c=cordova.ThemeableBrowser,o={image:"left",imagePressed:"left",align:"left",event:"custClosePressed"},n={image:"back",imagePressed:"back",align:"left",event:"backPressed"},(l=window.isIOS?[o,n]:[n,o]).push({image:"check",imagePressed:"check",align:"right",event:"pagesPressed"}),s={toolbar:{height:44,color:"#E03131"},customButtons:l,fullscreen:!1},p=!1,u=c.open(e,"_blank",s),d=function(e,a){if(!p)return p=!0,u.removeEventListener("custClosePressed",i),u.removeEventListener("backPressed",t),u.removeEventListener("loaderror",m),u.removeEventListener("pagesPressed",h),u.removeEventListener(cordova.ThemeableBrowser.EVT_ERR,m),u.close(),a?a({body:e}):r({body:e})},h=function(){return u.executeScript({code:"document.documentElement.innerHTML"},(function(t){var e,r;e=t[0];try{return e.match(/<body.*?>([\s\S]*)<\/body>/i)[1].replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,""),d(e)}catch(t){return r=t,console.log(r),d(e)}}))},m=function(t){return"Error: ".concat(t.code+" : "+t.message)},i=function(){return d("Cancelled",(function(){return window.location="/1.5/wecard"}))},t=function(){return u.executeScript({code:"window.history.back(-1)"})},"wechat"===a.getUrlSrc(e)&&u.addEventListener("loadstop",h),u.addEventListener("custClosePressed",i),u.addEventListener("backPressed",t),u.addEventListener("pagesPressed",h),u.addEventListener("loaderror",m),u.addEventListener(cordova.ThemeableBrowser.EVT_ERR,m)}))},setPreviewContents:function(t){var e,r,a,n;if(n=this,e=$("#frame-preview"),a=(r=$("#frame-preview")[0]).contentDocument||r.contentWindow.document,n.getContentFromCt||(n.getContentFromCt=window.getContentFromCt),a.body.innerHTML=n.getContentFromCt(t),a.body.style.backgroundColor="black",a.body.style.margin=0,!(e.contents().find("#animateCss").length>0))return e.contents().find("head").append($("<link/>",{rel:"stylesheet",href:"/css/animate.min.css?ver=2.6.0",type:"text/css",id:"animateCss"}))},getContentFromCt:function(t){return t.m||""===t.m?"user"===t._for?t.m:'<div style="" class="editPrev"> <div style="position: absolute;  z-index: 10;width: 100%;height: 100%;  min-height: 360px; background-size: 100% auto; background-position: center '+(t.bgPos||"center")+"; background-repeat: no-repeat; background-image: url('"+(t.bg||"")+'\'); background-color: black; "> <div class="animated long '+(t.ani||"fadeInUp")+' " style="'+(t.tbg||"")+"width: 100%;  color: white; padding: 10px; box-sizing: border-box; position: absolute; "+(t.pos||"top:10%;")+'">'+t.m+"</div> </div> </div>":"Error: no text"},setPreviewScale:function(t){var e,r,a,n,o,i,s,l,d,c;return l=function(t,e,r,a){var n;return r="scale("+r+")",(n=$("#frame-preview")).css("height",e+"px"),n.css("width",t+"px"),n.css("margin-left",a+"px"),n.css("transform",r),n.css("-webkit-transform",r)},d=function(t){var e,r,a,n,o;for(n=[],e=r=0,a=(o=["fs-tip-sm","fs-tip-md","fs-tip-lg","fs-tip-pd","fs-tip-pc"]).length-1;0<=a?r<=a:r>=a;e=0<=a?++r:--r)e===t?n.push($("#"+o[e]).css("display","block")):n.push($("#"+o[e]).css("display","none"));return n},d(t=parseInt(t)),c=(e=[[320,568],[375,627],[414,736],[708,1024],[1366,768]])[t][0],n=e[t][1],r=$("#frame-preview-wrapper").height(),a=$("#frame-preview-wrapper").width(),s=parseInt(a)/c,i=parseInt(r)/n,o=Math.min(s,i),l(Math.max(c*o,c),Math.max(n*o,n),o,c*o<a?(a-c*o)/2:0)},filterAndGetTitle:function(t,e){var r,a,n,o,i,s,l,d,c,u,p,m,h,g;o=function(t,e){var r,a;return r="",(a=document.createElement("a")).href=t,r=t.split("/")[0]+"//",r+=a.hostname,"domain"!==e&&(r+=a.pathname),r},m=function(t){var e,r,a;try{return!(a=$(t).attr("src"))||(e=o(amGloble.domain,"domain"),a.indexOf("http")>-1||a.indexOf("https")>-1||"data:image"===a.substr(0,10)||("/"===a[0]?$(t).attr("src",e+a):$(t).attr("src",e+"/"+a)),!0)}catch(t){return r=t,"undefined"!=typeof console&&null!==console?console.log(r):void 0}},p={},a=$("<div>").html(e),p.title=a.find("title").text(),p.title&&(p.title=p.title.replace(/realtor\s+force/i,"")),p.desc=a.find("meta[name=description]").attr("content"),i=e;try{a.find("img").each((function(){var e,r;return $(this).attr("style")&&$(this).attr("style",""),$(this).attr("onerror")&&$(this).attr("onerror",""),m(this),e=$(this).attr("data-src"),r=$(this).attr("src"),/^(http|https):\/\/mmbiz.qpic.cn/i.test(t)&&/^data:image/.test(r)&&(r=null),e&&!r&&$(this).attr("src",e),$(this).attr("onload")&&$(this).attr("onload",""),$(this).css("max-width","100%"),$(this).css("height","auto")})),a.find("iframe").each((function(){return $(this).attr("onerror")&&$(this).attr("onerror",""),$(this).attr("onload")&&$(this).attr("onload",""),$(this).attr("style","max-width:100%"),$(this).attr("src")?m(this):($(this).remove(),!0)})),a.find("video").each((function(){return $(this).attr("onerror")&&$(this).attr("onerror",""),null==$(this).attr("controls")&&$(this).attr("controls",""),$(this).attr("onloadonloadstart")&&$(this).attr("onloadonloadstart",""),$(this).attr("style","max-width:100%"),m(this)})),a.find("a").each((function(){var t,e,r;if($(this).attr("style")&&$(this).css("position","relative"),r=$(this).attr("href")){if((e=/brjtools\.cn|schoolinfo\.ca|9lms\.com|realsforce\.ca|realtorforce\.ca/i).test(amGloble.domain)&&0!==r.indexOf("http")||e.test(r))return $(this).remove(),!0;if(t=o(amGloble.domain),0!==r.indexOf("http"))return $(this).attr("href",t+"/"+r)}})),i=a.html()}catch(t){n=t,"undefined"!=typeof console&&null!==console&&console.log(n),i+="Error : ".concat(n.message||n.toString())}switch(h=this.getUrlSrc(t),g="",/^(https|http):\/\/youtu\.be\/([a-zA-Z\d-]+)*/i.test(t)&&(g="youtubeApp"),h){case"wechat":for(i=(i=(i=(i=(i=(i=i.replace(/<head[^>]*>[\s\S]*?<\/head>/g,"")).replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/g,"")).replace(/<css[^>]*>[\s\S]*?<\/css>/g,"")).replace(/<style[^>]*>[\s\S]*?<\/style>/g,"")).replace(/<link\b.+href=.*>/g,"")).replace(/<meta\b.+name=.*>/g,""),a=$("<div>").html(i),l=0,d=(c=["#activity-name","js_cmt_mine",".rich_media_title","title",".rich_media_meta_text",".rich_media_meta_nickname","#js_view_source"]).length;l<d;l++)s=c[l],a.find(s).remove();i=a.html();break;case"youtube":i="<div>",i+='<iframe width="100%" height="315" src="https://www.youtube.com/embed/'+("youtubeApp"===g?t.split(".be/")[1]:t.split("watch?v=")[1]),i+='" frameborder="0" allowfullscreen></iframe>',i+="</div>";break;default:if(i=(i=(i=(i=i.replace(/<head[^>]*>[\s\S]*?<\/head>/g,"")).replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/g,"")).replace(/<css[^>]*>[\s\S]*?<\/css>/g,"")).replace(/<style[^>]*>[\s\S]*?<\/style>/g,""),(r=(a=$("<div>").html(i)).find("article"))&&r.length>0&&(null!=(u=r.html())?u.length:void 0)>800)try{a=$("<div>").html(r.wrap("<p>").parent().html())}catch(t){n=t,console.log(n.toString())}a.filter(".header, .menu, .nav, .box-fix-l, .box-fix-r").remove(),a.filter("link, select, input, button, meta, footer, nav, form").remove(),a.find("*").each((function(){var t,e,r,a,o,i,s;if(s=$(this).prop("tagName").toLowerCase(),a=$(this).parent()?$(this).parent().prop("tagName").toLowerCase():"","img"!==s&&"iframe"!==s&&"video"!==s&&"a"!==s){if(o=$(this).attr("role"),i=$(this).attr("class")&&$(this).attr("class").length>0?$(this).attr("class").split(" ")[0]:"",e=$(this).css("display"),t=$(this).text().length,"navigation"===o||"contentinfo"===o||"banner"===o||"search"===o||"none"===e||t<0||"li"===s&&"ul"!==a&&"li"===s&&"ol"!==a||"select"===s||"input"===s||"button"===s||"link"===s||"meta"===s||"footer"===s||"nav"===s||"form"===s||"base"===s||"header"===i||"menu"===i||"nav"===i||"box-fix-l"===i||"box-fix-r"===i)return $(this).remove(),!0;if("a"===s){try{$(this).replaceWith("<span>"+$(this).text()+"<span>")}catch(t){n=t,RMSrv.dialogAlert(n.toString()),console.log(n.toString())}return!0}if((r=$(this).attr("style"))&&$(this).css("position","relative"),r&&$(this).css("height","auto"),r=$(this).attr("style"),$(this).attr("onerror")&&$(this).attr("onerror",""),$(this).attr("onclick")&&$(this).attr("onclick",""),$(this).attr("onload")&&$(this).attr("onload",""),$(this).attr("class")&&$(this).attr("class",""),$(this).attr("id"))return $(this).attr("id","")}})),i="<div style='font-size: 14px;'>"+(i=a.html())+"</div>"}return p.body=i,p},savePropCard:(e=_asyncToGenerator(_regeneratorRuntime().mark((function t(e){var r,a,n,o;return _regeneratorRuntime().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return o=this,this._datas.pageData.card.meta.title=this._doms.metaTitle.val(),this._datas.pageData.card.meta.editor=this._doms.metaEditor.val(),this._datas.pageData.card.meta.desc=this._doms.metaDesc.val(),this._datas.pageData.card.meta.custvc=this._doms.metaVc.val(),this._datas.pageData.card.meta.addr=this._doms.metaAddr.val(),this._datas.pageData.card.meta.tp=this._datas.pageData.card.tp,$("#share-title").html(this._doms.metaTitle.val()),$("#share-desc").html(this._doms.metaDesc.val()),n=$("#share-avt").html(),o.isOwnImg(e.meta.img)||RMSrv.ver>="3.1.0"?$("#share-image").html(e.meta.img+"#"):$("#share-image").html(n),(null!=(r=e.meta.img)?r.indexOf("/img/noPic.png"):void 0)>-1&&$("#share-image").html(n),$("#loading-bar-spinner").css("display","block"),t.prev=13,t.next=16,RMSrv.fetch(amGloble.savePropCard,{method:"POST",body:e});case 16:return a=t.sent,$("#loading-bar-spinner").css("display","none"),a.success?(o._datas.pageData.card._id=a._id,a._id&&(window._id=a._id),o.enableBtns()):console.log("Save Error"),t.abrupt("return");case 22:return t.prev=22,t.t0=t.catch(13),t.t0,$("#loading-bar-spinner").css("display","none"),flashMessage("server-error"),t.abrupt("return");case 28:return t.abrupt("return",null);case 29:case"end":return t.stop()}}),t,this,[[13,22]])}))),function(t){return e.apply(this,arguments)}),getData:function(){var t,e,r,a;if(a=this,e=function(t){return{done:function(t){return this._successCallback=t,this},fail:function(e){var r=this;return this._errorCallback=e,RMSrv.fetch(t.url,{method:t.type||"POST",body:t.data||{}},(function(t,e){return t?"function"==typeof r._errorCallback?r._errorCallback(t):void 0:"function"==typeof r._successCallback?r._successCallback(e):void 0})),this}}},(t=function(t,r){var n;return"edit"===r?(n=function(t){var e;return(e=a._datas.pageData=t).success?(window._id=e._id,a.initData()):RMSrv.dialogAlert(e.err)},{fn:e,cfg:{url:amGloble.getPropCardInfo,type:"get",data:{}},done:n}):"topic"===t?{update:function(){return $("#loading-bar-spinner").css("display","block")},fn:a.getPageContent,data:amGloble.domain,cb:function(t){var e;return $("#loading-bar-spinner").css("display","none"),t?(e=a._datas.pageData=a.filterAndGetTitle(amGloble.domain,t.body),amGloble.newsId?RMSrv.fetch(amGloble.getNewsTitle,{method:"POST",body:{}},(function(t,r){if(!t)return r?(e.title=r.tl,e.desc=r.desc,a.initData()):flashMessage("server-error");flashMessage("server-error")})):a.initData()):(flashMessage("url-error"),setTimeout((function(){return window.location="/1.5/wecard"}),1e3))}}:"listing"===t?(n=function(t){var e;return t.success?(e=a._datas.pageData=t).card&&JSON.stringify(e.card).length<=2?RMSrv.fetch(amGloble.getPropDetail,{method:"POST",body:{}},(function(t,e){if(!t)return e?(a._datas.pageData.prop=e.detail,a.initData()):flashMessage("server-error");flashMessage("server-error")})):a.initData():RMSrv.dialogAlert("cannot get listing!")},{update:function(){},fn:e,cfg:{url:amGloble.getPropCardInfo,type:"get",data:{}},done:n}):indexOf.call(amGloble.templateTypes,t)>=0?{update:function(){},fn:e,cfg:{url:amGloble.getTemplate,type:"get",data:{type:t}},done:function(t){return t.success?(a._datas.pageData=t.content,a.initData()):flashMessage("server-error")}}:(alert("error! unknown type of data"),RMSrv.dialogAlert("Error: unknown type of data"))}(amGloble.type.toLowerCase(),null!=(r=amGloble.action)?r.toLowerCase():void 0)).update&&t.update(),t.cfg||(t.cfg=t.data),t.done&&t.fn(t.cfg).done(t.done).fail((function(){flashMessage("server-error")})),t.cb)return t.fn(a,t.cfg,t.cb)},getMusicLinks:function(){var t;return t=this,RMSrv.fetch(amGloble.getMusicList,{method:"GET",body:{}},(function(e,r){var a,n,o,i;if(!e){if(r){for(a=[],o=0,i=(n=t._datas.musicList=r).l.length;o<i;)a.push("<li urls="+n.l[o].url+" n="+n.l[o].nm+'><a href="#" adid="musicPlayer'+o+'" class="mp3 icon"><audio id="musicPlayer'+o+'" loop="" src="'+n.l[o].url+'"  style="display:none;position:absolute;z-index:-11"></audio></a><span>'+n.l[o].nm+"</span>"),o++;return $("#bgs-mp3-ul").empty(),$("#bgs-mp3-ul").html(a.join(""))}return flashMessage("server-error")}flashMessage("server-error")}))},isOwnImg:function(t){var e;return!!t&&(e=function(t){return 0===t.indexOf("http")},!e(t)||!!/^(https|http):\/\/([a-zA-Z\d-]+\.)*realmaster\.(com|cn)(\/\w+)+.(jpg|png)/i.test(t))},setMetaImg:function(t,e){var r,a,n,o,i,s;if(s=this,a=function(t){var e,r;return 0===t.indexOf("http")||0===t.indexOf("data:image")||t.length>1500?t:(e="",(r=document.createElement("a")).href=document.URL,e=document.URL.split("/")[0]+"//",(e+=r.hostname)+t)},i=function(t,e){var r,n,o,i;i=function(t){var e,r,a,o;for(a=[],e=0,r=t.length;e<r;e++)o=t[e],/^(http|https):\/\/mmbiz.qpic.cn/i.test(n.src)&&(o.src=o.src.replace("/0?","/320?"),a.push(o));return a};try{r=$("<div>").html($((null!=t?t.trim():void 0)||""))}catch(e){t="<div>"+((null!=t?t.trim():void 0)||"")+"</div>",r=$(t)}if(o=$("img",r),n=0,0===o.length)return e.img="/img/noPic.png",e;for(;n<=o.length-1;){if(o[n].height>110&&s.isOwnImg(o[n].src))return e.img=a(o[n].src),e;n++}for(n=0,i(o);n<=o.length-1;){if(o[n].height>140)return e.img=a(o[n].src),e;n++}return e},Array.isArray(t)){for(r=0;r<=t.length-1;){if(s._datas.pageData.card&&s.isFlyer(s._datas.pageData.card.tp,null!=(n=s._datas.pageData.card.meta)?n.shSty:void 0))return e.img=a(t[0].bg),e;if(null!=(o=i(t[r].m,e))?o.img:void 0)return o;if(r===t.length-1)return e.img="/img/noPic.png",e;r++}return e}return i(t,e)},initData:function(){var t,e,r,a,n,o,i,s,l,d,c,u,p,m,h,g,f,v,w,y,b,_,S,k,x;if(w=this,S=(a=this._datas.pageData).user||{},r=a.card||{},g=a.prop||{},m=r.meta||{shSty:"blog"},_=amGloble.type,t=amGloble.action,null==r.tp&&(r.tp=_),r.id=amGloble.id,window._id&&(r._id=window._id),r.meta||(r.meta={shSty:"blog"}),window.shSty=m.shSty,c=function(t,e){var r;return t.nki.attr("src",e.avt||"/img/logo.png"),t.nknm.html((e.fn||"")+" "+(e.ln||"")),t.ctctWx.html(e.wx),e.qrcd&&t.ctctWxQr.attr("src",e.qrcd),e.grpqrcd?t.ctctGrpQrcd.attr("src",e.grpqrcd):t.ctctGrpQrcdW.css("display","none"),t.ctctTel.html(e.mbl),t.ctctEml.html("<a style='color:white' href='mailto:"+e.eml+"?Subject=Hi%20From%20RealMaster'>"+e.eml+"</a>"),r=e.web?e.web.indexOf("http")>-1?e.web:"http://"+e.web:"",t.ctctWeb.html("<a style='color:white' href='"+r+"'>"+(e.web||"")+"  </a>"),t.ctctCpny.html(e.cpny),t.ctctCpny_pstn.html(e.cpny_pstn)},"edit"!==t){if("listing"===_){if(r.ml_num=amGloble.ml_num,JSON.stringify(r).length<=200||!r.seq)for(amGloble.shSty&&(r.meta.shSty=amGloble.shSty),this._doms.propType.html(g.type_own1_out+" "+(g.style||"")+(g.prop_type||"")+(g.bus_type||"")),this._doms.propBr.html(g.br+" + "+g.br_plus),this._doms.propKit.html(g.num_kit+" + "+g.kit_plus),this._doms.propPak.html((g.gar_spaces||"")+" "+(g.gar_type||"")+" "+g.park_spcs),this._doms.propBsmt.html(g.bsmt1_out+" "+(g.bsmt2_out||"")),this._doms.propBath.html(g.bath_tot+" "+(g.bath_details||"")),this._doms.propLot.html((g.front_ft||"")+" x "+(g.depth||"")+" "+(g.lotsz_code||"")+" "+(g.irreg||"")),this._doms.propExt.html(g.constr1_out+" "+g.constr2_out),this._doms.propTax.html(g.taxes+" / "+g.yr),this._doms.propSqft.html(g.sqft),this._doms.propAC.html(g.a_c),this._doms.propCVC.html(g.central_vac),this._doms.propAge.html(g.yr_built),this._doms.propPool.html(g.pool),this._doms.propFuel.html(g.fuel+" "+g.heating),this._doms.propRltr.html(g.rltr),this._doms.propRemark.html(g.ad_text),this._doms.propPrice.html("$"+g.lp_price),c(this._doms,S),o=$('li[data-role="prop-detail-pane"]').html(),v=$('li[data-role="prop-remark-pane"]').html(),x=$('li[data-role="ctct"]').html(),r.uid=S.id,r.music={nm:"basical",url:"/musics/baical.MP3"},r.bkgimg={url:"/wecardBgs/bg2.jpg",nm:"bg2.jpg"},r.seq=[{_for:"detail",m:o},{_for:"remark",m:v}],"blog"!==amGloble.shSty&&r.seq.push({_for:"user",m:x}),n="<div class='des'><p>"+g.addr+" "+g.municipality+" "+g.county+"</p><p>"+g.type_own1_out+" "+g.style+"</p></div>",(l=getTrebPicUrls(g.pic_num,a.ml_num)).length||l.push(window.location.origin+"/img/noPic.png"),s=l.length-1;s>=0;)0===s&&(r.meta.img=l[s]),d="<img src='"+l[s]+"' alt='"+s+"' style='width:100%;'></img>",y="vt"===amGloble.shSty?{_for:"pic",pos:"top:10%;",ani:"fadeInUp",bg:l[s],m:n,bgPos:"center"}:{_for:"pic",m:d+n},r.seq.unshift(y),s--;return a.card=r,a.user=S,this._datas.pageData=a,this.writeHtml()}if("topic"===_)return r={},e=(a=this._datas.pageData).body||"Empty Content",m=r.meta={shSty:"blog"},r.uid=S.id,a.card=r,a.card.meta=w.setMetaImg(e,m),r.tp="topic",m.title=a.title||"",r.music={nm:"basical",url:"/musics/baical.MP3"},r.bkgimg={url:"/wecardBgs/bg2.jpg",nm:"bg2.jpg"},r.seq=[{_for:"topic",m:"<div id='topic-content'>"+e+"</div>"}],this._datas.pageData.card=r,this.writeHtml();if(indexOf.call(amGloble.templateTypes,_)>=0){if(r={},h={},b={shSty:"blog"},"xmas1"===_?h={nm:"xmas1",url:"/musics/We_wish_you_a_merry_Christmas_clip.MP3"}:"xmas2"===_?h={nm:"xmas2",url:"/musics/Jingle_Bells_clip.MP3"}:"spring_fest"===_&&(h={nm:"spring_fest",url:"/musics/spring_fest.MP3"}),(a=this._datas.pageData).card.music=h,e=a.card.seq||[],w.isFlyer(_)){for(b={shSty:"vt"},u=0,p=(f=a.card.seq).length;u<p;u++)"user"===(s=f[u])._for&&(i=!0);i||(c(this._doms,S),k={_for:"user",m:x=$('li[data-role="ctct"]')[0].innerHTML},a.card.seq.push(k))}return m=r.meta=b,a.card.meta=w.setMetaImg(e,m),this.writeHtml()}return console.log("undefined type init data "+_),alert("Error unknown type write data"),RMSrv.dialogAlert("Error unknown type write data")}this.enableBtns(),this.writeHtml()},setMeta:function(t){if(this._doms.metaTitle.val((null!=t?t.title:void 0)||this._doms.metaTitle.val()),this._doms.metaEditor.val((null!=t?t.editor:void 0)||this._doms.metaEditor.val()),this._doms.metaDesc.val((null!=t?t.desc:void 0)||this._doms.metaDesc.val()),this._doms.metaVc.val((null!=t?t.custvc:void 0)||this._doms.metaVc.val()),this._doms.metaAddr.val((null!=t?t.addr:void 0)||this._doms.metaAddr.val()),$("#share-title").html(this._doms.metaTitle.val()),$("#share-desc").html(this._doms.metaDesc.val()),this.isOwnImg(t.img)||RMSrv.ver>="3.1.0")return $("#share-image").html(t.img+"#")},writeHtml:function(){var t,e,r,a,n,o,i,s,l,d,c,u,p,m,h,g,f,v,w,y,b,_,S,k,x,C,M,P,D,L,I,T,E;if(l=this._datas.pageData,f="<li class='item-add' dataRole='new-frame' style='height: 73px; text-align: center; font-size:27px; padding-top: 20px;'><div><a id='newFrame' class='fa fa-plus-circle' href='#', style='color: #666' /></div></li>",s='<a href=\'#\' class=\'btn-r btn-edit fa fa-edit edit-in-summernote\' /></a><a href="#" class="btn-r btn-delete  fa fa-trash" /></a><a href="#" class="btn-r btn-sort  fa fa-chevron-circle-up" /></a><a href="#" types= _for class="btn-r btn-see fa fa-eye" /></a>',this._doms.ctrlButtons=s,!l||l.e)return flashMessage("server-error");if(T=amGloble.type.toLowerCase(),a=l.card,E=l.user,"listing"===T&&"create"===amGloble.action){for(u=[],(D=a.seq)||(D=[]),d=(v=l.prop).lp_price+" For "+v.s_r+" "+v.addr+" "+v.municipality+" "+v.county+" "+v.type_own1_out+" "+v.style,this._doms.metaDesc.html(d),I=v.addr+" "+v.municipality+" ",this._doms.metaTitle.val(I),r=I+", "+(v.municipality_district||"")+", "+(v.county||""),this._doms.metaAddr.val(r),p=0,m=D.length;p<m;)n=D[p],e=(e="vt"===amGloble.shSty?$(this.getContentFromCt(n)):$("<div>"+n.m+"</div>")).wrap("<li class='item-img edit-in-summernote' dataRole='prop-img-pane'><div></div></li>").parent().parent(),"vt"!==amGloble.shSty&&(n.m=null!=(w=e.children("div"))&&null!=(b=w[0])?b.outerHTML:void 0),e.append(s),u.push(e),p++;u.push(f)}else if("topic"===T){if(l.body,u=[],!(D=a.seq))return void(D=[]);for(this.setMeta(a.meta),p=0,m=D.length;p<m;)c='<li class="edit-in-summernote">'+((n=D[p]).m||"")+"</li>",(e=$(c)).append(s),u.push(e),p++;this._doms.topic.append(s),u.push(f),$("#edit-page-contents-ul").html(u),$("#topic-content").css("overflow-x","hidden"),"edit"!==(null!=(_=amGloble.action)?_.toLowerCase():void 0)&&null!=(S=$("#edit-page-contents-ul li:first-child"))&&null!=(k=S[0])&&k.click()}else if(indexOf.call(amGloble.templateTypes,T)>=0||"edit"===(null!=(x=amGloble.action)?x.toLowerCase():void 0)){for(D=a.seq||[],L=null!=(g=a.meta)?g.shSty:void 0,u=[],p=0,m=D.length;p<m;){n=D[p];try{o=$((null!=n&&null!=(C=n.m)?C.trim():void 0)||"<section></section>")}catch(t){o=$("<section>"+((null!=n&&null!=(M=n.m)?M.trim():void 0)||"")+"</section>")}"assignment"!==T&&"exlisting"!==T&&"event"!==T||(t=$(n.m)).find("[data-role=tpl-nm]").length&&(t.find("[data-role=tpl-nm]").html((E.fn||"")+" "+(E.ln||"")),t.find("[data-role=tpl-tel]").html(E.mbl),t.find("[data-role=tpl-tel-call]").attr("href","tel:"+E.mbl),n.m=t[0].outerHTML),h=o.hasClass("dis")?'<li class="dis edit-in-summernote"><div>':'<li class="edit-in-summernote"><div>',this.isFlyer(T,L)&&"user"!==n._for?(i=h+(i=this.getContentFromCt(n))+"</div></li>",e=$(i)):(n.m=h+n.m+"</div></li>",e=$(n.m),n.m=null!=(P=e.children("div"))&&null!=(y=P[0])?y.innerHTML:void 0),e.append(s),u.push(e),p++}u.push(f),this.setMeta(g)}else alert("unknown type write html type: "+amGloble.type.toLowerCase()),u=[];return $("#edit-page-contents-ul").html(u),$("#edit-page-contents-ul").css("display","block"),a.meta.img?$("#thumbImg").attr("src",a.meta.img):void 0}},t.init()})),$(document).ready((function(){var t;return window.selectTpMap={listing:"Listing",event:"Event",exlisting:"Exclusive Listing",assignment:"Assignment",blog:"Blog",xmas1:"Flyer",xmas2:"Flyer",spring_fest:"Flyer"},t=function(t){return window.keyboardHeight=t.keyboardHeight},window.addEventListener("native.keyboardshow",t),initSummernote()})),function(){var t,e;t=function(t){var e,r;for(e=void 0,r=document.querySelectorAll(".segmented-control .control-item");t&&t!==document;){for(e=r.length;e--;)if(r[e]===t)return t;t=t.parentNode}},e=function(e){var r,a,n,o,i,s,l;if(a=void 0,r=void 0,s=void 0,o="."+(n="active"),(l=t(e.target))&&((a=l.parentNode.querySelector(o))&&a.classList.remove(n),l.classList.add(n),l.hash&&(s=document.querySelector(l.hash)))){for(r=s.parentNode.querySelectorAll(o),i=0;i<r.length;)r[i].classList.remove(n),i++;return s.classList.add(n)}},window.addEventListener("touchend",e),window.addEventListener("click",e)}();
