"use strict";function _regeneratorRuntime(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_regeneratorRuntime=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof m?t:m,a=Object.create(i.prototype),u=new I(r||[]);return o(a,"_invoke",{value:A(e,n,u)}),a}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var f="suspendedStart",h="suspendedYield",v="executing",g="completed",p={};function m(){}function y(){}function S(){}var w={};c(w,a,(function(){return this}));var R=Object.getPrototypeOf,M=R&&R(R(C([])));M&&M!==n&&r.call(M,a)&&(w=M);var b=S.prototype=m.prototype=Object.create(w);function E(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function L(e,t){function n(o,i,a,u){var l=d(e[o],e,i);if("throw"!==l.type){var c=l.arg,s=c.value;return s&&"object"==_typeof(s)&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,a,u)}),(function(e){n("throw",e,a,u)})):t.resolve(s).then((function(e){c.value=e,a(c)}),(function(e){return n("throw",e,a,u)}))}u(l.arg)}var i;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return i=i?i.then(o,o):o()}})}function A(t,n,r){var o=f;return function(i,a){if(o===v)throw Error("Generator is already running");if(o===g){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var u=r.delegate;if(u){var l=_(u,r);if(l){if(l===p)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===f)throw o=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=v;var c=d(t,n,r);if("normal"===c.type){if(o=r.done?g:h,c.arg===p)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(o=g,r.method="throw",r.arg=c.arg)}}}function _(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,_(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),p;var i=d(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,p;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,p):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,p)}function k(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function B(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function I(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(k,this),this.reset(!0)}function C(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(_typeof(t)+" is not iterable")}return y.prototype=S,o(b,"constructor",{value:S,configurable:!0}),o(S,"constructor",{value:y,configurable:!0}),y.displayName=c(S,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,S):(e.__proto__=S,c(e,l,"GeneratorFunction")),e.prototype=Object.create(b),e},t.awrap=function(e){return{__await:e}},E(L.prototype),c(L.prototype,u,(function(){return this})),t.AsyncIterator=L,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new L(s(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},E(b),c(b,l,"Generator"),c(b,a,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=C,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(B),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return u.type="throw",u.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,p):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),B(n),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;B(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:C(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),p}},t}function asyncGeneratorStep(e,t,n,r,o,i,a){try{var u=e[i](a),l=u.value}catch(e){return void n(e)}u.done?t(l):Promise.resolve(l).then(r,o)}function _asyncToGenerator(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){asyncGeneratorStep(i,r,o,a,u,"next",e)}function u(e){asyncGeneratorStep(i,r,o,a,u,"throw",e)}a(void 0)}))}}function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}var RMSrv,confirmJump,handleOpenURL,jumpURL,onNotification,onNotificationAPN;RMSrv={getCookie:function(e){var t,n,r,o,i;for(i=e+"=",r=0,o=(n=document.cookie.split(";")).length;r<o;r++){for(t=n[r];" "===t.charAt(0);)t=t.substring(1);if(0===t.indexOf(i))return t.substring(i.length,t.length)}return""},init:function(){var e;return this.geolocation=!1,this.ver=this.getCookie("apsv"),this.ver<"3.2"&&navigator.geolocation&&(this.geolocation=navigator.geolocation),(e=function(e){var t;return!!(t=(e=(e||navigator.userAgent).toLowerCase()).match(/android\s([0-9\.]*)/))&&t[1]})()&&parseFloat(e())<4.4&&(window.oldVerBrowser=!0),this.bindEvents()},getGeoPosition:function(e){var t,n;return n=function(t){if(e)return e(t)},t=function(t){return e&&e({err:t}),console.log(t.toString())},(window.LocationServices?window.LocationServices:(null!=RMSrv?RMSrv.geolocation:void 0)?RMSrv.geolocation:navigator.geolocation).getCurrentPosition(n,t)},fetch:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0;if("string"!=typeof e)throw new TypeError("fetch方法的第一个参数必须是URL字符串，实际类型: ".concat(_typeof(e)));return"function"==typeof n&&(r=n,n={}),null==n.method&&(n.method="POST"),null==n.credentials&&(n.credentials="same-origin"),null==n.headers&&(n.headers={}),n.headers.Accept="application/json","GET"===n.method.toUpperCase()?delete n.body:("POST"===n.method.toUpperCase()&&null==n.body&&(n.body={}),n.body&&"object"===_typeof(n.body)&&(n.headers["Content-Type"]="application/json",n.body=JSON.stringify(n.body))),n.useNativeFetch?(delete n.useNativeFetch,RMSrv.action({tp:"fetch",opt:n,url:e},r)):(t=function(){var t=_asyncToGenerator(_regeneratorRuntime().mark((function t(){var o,i,a;return _regeneratorRuntime().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,window.fetch(e,n);case 3:if((i=t.sent).ok){t.next=8;break}throw(o=new Error("HTTP ".concat(i.status,": ").concat(i.statusText))).response=i,o;case 8:return t.next=10,i.json();case 10:if(a=t.sent,!r){t.next=13;break}return t.abrupt("return",r(null,a));case 13:return t.abrupt("return",a);case 16:if(t.prev=16,t.t0=t.catch(0),o=t.t0,!r){t.next=23;break}r(o,null),t.next=24;break;case 23:throw o;case 24:case"end":return t.stop()}}),t,null,[[0,16]])})));return function(){return t.apply(this,arguments)}}(),t())},bindEvents:function(){return document.addEventListener("deviceready",this.onDeviceReady,!1)},onDeviceReady:function(){var e,t;return(e=null!=(t=device.manufacturer)?t.toLowerCase():void 0)&&["xiaomi","huawei"].indexOf(e)>=0&&delete window.LocationServices,RMSrv.setupMenuBtn(),RMSrv.setupBackBtn(),RMSrv.regDevice(),RMSrv._setFileChooser(),RMSrv._getKeyboard(),RMSrv.ready=!0},enableMenuButton:function(){return RMSrv._disableMenuKey=!enabled},setupMenuBtn:function(){return document.addEventListener("menubutton",(function(){if(!RMSrv._disableMenuKey)return window.location="/1.5/settings"}),!1)},enableBackButton:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return RMSrv._disableBackKey=!e},setupBackBtn:function(){return document.addEventListener("backbutton",(function(e){if(!RMSrv._disableBackKey)return document.getElementById("top-page")?(e.preventDefault(),navigator.notification.confirm("Quit?",(function(e){if(1===e)return navigator.app.exitApp()}))):document.getElementById("news-page")?(e.preventDefault(),window.location="/1.5/index"):document.getElementById("wecard-list-page")?(e.preventDefault(),window.location="/1.5/settings"):document.getElementById("dl-share-content")?window.location.href=document.referrer:document.getElementById("srvEle")?(e.preventDefault(),toggleModal("propDetailModal","close")):navigator.app.backHistory()}),!1)},scanQR:function(e){return cordova.plugins.barcodeScanner.scan((function(t){return RMSrv.QRsuccess(t,e)}),RMSrv.QRfailed)},QRsuccess:function(e,t){return e.cancelled?null:setTimeout((function(){return"QR_CODE"!==e.format?RMSrv.dialogAlert("Scan Error. Please try again."):e.text.match(/(https|http):\/\/([a-z0-9\.\-\_]+)/i)?"function"==typeof t?t(e.text):window.location=(t||"/1.5/iframe?u=")+encodeURIComponent(e.text):RMSrv.dialogAlert("Unknown Code : "+e.text)}),10)},QRfailed:function(e){return RMSrv.dialogAlert("Scan Failed: "+e)},isIOS:function(){var e,t;return t=("undefined"!=typeof navigator&&null!==navigator?navigator.userAgent:void 0)||"",!!/iPhone|iPad|iPod|CFNetwork/i.test(t)||(e=("undefined"!=typeof navigator&&null!==navigator?navigator.platform:void 0)||"",!!/^(iPhone|iPod|iPad)$/i.test(e)||"MacIntel"===e&&(("undefined"!=typeof navigator&&null!==navigator?navigator.maxTouchPoints:void 0)||0)>1)},isAndroid:function(){return/Android/i.test(navigator.userAgent)},isWeChat:function(){return/MicroMessenger/i.test(navigator.userAgent)},isBlackBerry:function(){return/BlackBerry/i.test(navigator.userAgent)},appendDomain:function(e){var t;return(t=window.location.href.split("/"))[0]+"//"+t[2]+e},showInBrowser:function(e){if(/^(http|https)/.test(e)||(e=this.appendDomain(e)),RMSrv.ready)return window.open(e,"_system")},openTBrowser:function(e,t){return/^(http|https)/.test(e)||(e=this.appendDomain(e)),cordova.ThemeableBrowser?cordova.ThemeableBrowser.open(e,"_blank",t):window.open(e,"_blank")},openInAppBrowser:function(e){var t,n,r,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"location=false";return r=window.open(encodeURI(e),"_blank",o),n=function(){var e;return r.removeEventListner("loadstop"),r.removeEventListner("loadstart"),e&&(clearInterval(e),e=null),r.close()},t=function(e,t){if(e.url.match("in_app_close")&&n(),t)return r.executeSript({code:"function(){var _ref;(_ref = document.getElementById('goback-link')) != null ? _ref.href = '/in_app_close' : void 0;return true;}.();"})},r.addEventListener("loadstop",(function(e){return t(e,!0)})),r.addEventListener("loadstart",(function(e){return t(e,!1)})),setInterval((function(){return r.executeScript({code:"window.location"},(function(e){if(e.toString().match("/tools"))return n()}))}),2e3)},dialogAlert:function(e){return navigator.notification.alert("string"==typeof e?e:e.message||e.toString())},dialogConfirm:function(e,t,n,r){return r||n&&Array.isArray(n)&&(r=n,n="Confirm"),navigator.notification.confirm(e.toString(),t,n,r)},isDBG:function(){return/i\.realmaster/i.test(window.location.hostname||/app\.test/i.test(window.location.hostname))},fDoc:function(){var e,t,n;if(e=null,n=document.getElementById("iframe"))try{null==(e=n.contentDocument)&&(e=document),e.document&&(e=e.document)}catch(e){t=e,console.log(t)}return e},getMeta:function(e){var t,n,r,o,i;for(o=e.querySelectorAll("meta"),i={title:e.title},t=0,n=o.length;t<n;t++)i[(r=o[t]).getAttribute("name")]=r.getAttribute("content");return i},getShareImage:function(e){var t,n,r,o,i;if(t=e.getElementById("content_div")||e.body)for(n=0,o=(i=t.getElementsByTagName("img")).length;n<o;n++)if(null!=(r=i[n])?r.src:void 0)return r.src;return"https://realmaster.com/img/logo.png"},logoImg:"data:image/png;base64,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",resizeImage:function(e,t,n,r){var o,i,a,u;return i=function(e){return i=null,r(e||RMSrv.logoImg)},o=e.createElement("canvas"),(a=e.createElement("img")).onload=function(){var e,t,r,a,u,l;try{if(l=u=this.width,a=r=this.height,u>r?u>n&&(l=n,a=Math.round(r/u*n)):r>n&&(a=n,l=Math.round(u/r*n)),o.width=l,o.height=a,o.getContext("2d").drawImage(this,0,0,l,a),e=o.toDataURL("image/png"),i)return i(e)}catch(e){if(t=e,RMSrv.isDBG()&&RMSrv.dialogAlert("Error:"+t),i)return i()}},a.onerror=function(e){if(RMSrv.isDBG()&&(alert("resizeImage Error:"+a.src),alert(JSON.stringify(e))),i)return i()},a.setAttribute("crossOrigin","anonymous"),a.crossOrigin="",(u=/^(.*\?v=)\d+$/g.exec(t))?a.src=u[1]+Date.now():"#"===t[t.length-1]?(t=t.substring(0,t.length-1),a.src=t+"?v="+Date.now()):/f\.i\.realmaster.*\.(jpg|png)$/.test(t)||/f\.realmaster.*\.(jpg|png)$/.test(t)?a.src=t+"?v="+Date.now():a.src=t,null},hasWechat:function(e){return RMSrv.onReady((function(){return("undefined"!=typeof Wechat&&null!==Wechat?Wechat.isInstalled:void 0)?Wechat.isInstalled((function(t){return e(t)}),(function(){return e(!1)})):e(!1)}))},wechatAuth:function(){return RMSrv.onReady((function(){return"undefined"!=typeof Wechat&&null!==Wechat?Wechat.auth("snsapi_userinfo",(function(e){var t;if(RMSrv.isAndroid()&&(null!=e&&null!=(t=e.code)?t.length:void 0)>10)return window.location.href="/scheme?code="+e.code}),(function(e){return location.reload(),"undefined"!=typeof console&&null!==console?console.log(e):void 0})):void 0}))},wechatShareError:function(e){switch(null!=e?e.toString():void 0){case"ERR_USER_CANCEL":break;case"ERR_WECHAT_NOT_INSTALLED":return RMSrv.dialogAlert("WeChat Not Installed");case"ERR_UNKNOWN":window.onerror("WeChat Sharing ERR_UNKNOWN",window.location.href,"");break;default:return RMSrv.dialogAlert(e.toString())}},wechatShare:function(e,t,n){var r;return RMSrv.ver>="3.1.0"&&"undefined"!=typeof Wechat&&null!==Wechat?(r={message:{title:t.title||"RealMaster Sharing",description:t.description||"RealMaster App Sharing",thumb:t.image,media:{type:Wechat.Type.WEBPAGE,webpageUrl:t.url.replace("realmaster.com","realmaster.cn")}},scene:n},RMSrv.shared(t,!0),void Wechat.share(r,(function(){return RMSrv.shared(t)}),(function(n){return"发送请求失败"===n?RMSrv.resizeImage(e,t.image,100,(function(e){return r.message.thumb=e,Wechat.share(r,(function(){return RMSrv.shared(t)}),RMSrv.wechatShareError)})):RMSrv.wechatShareError(n)}))):RMSrv.resizeImage(e,t.image,100,(function(e){return r={title:t.title||"RealMaster Sharing",description:t.description||"RealMaster App Sharing",thumbData:e.split(",")[1],url:t.url.replace("realmaster.com","realmaster.cn")},WeChat.share(r,n,(function(){return RMSrv.shared(t)}),RMSrv.wechatShareError)}))},facebookLogin:function(e){return facebookConnectPlugin.login(["public_profile"],e,(function(e){return RMSrv.dialogAlert("Login Error:"+JSON.stringify(e))}))},facebookLoginNShare:function(e,t,n){return RMSrv.facebookLogin((function(){return RMSrv.facebookShare(e,t,n)}))},_fbErrorCounter:0,facebookShare:function(e,t,n){var r;return r={method:n,picture:t.image,link:t.url,caption:t.title||"RealMaster Sharing",description:t.description||"RealMaster App Sharing"},facebookConnectPlugin.showDialog(r,(function(){return RMSrv.shared(t)}),(function(r){return RMSrv._fbErrorCounter++>1?(RMSrv.dialogAlert("Dialog Error:"+JSON.stringify(r)),void(RMSrv._fbErrorCounter=0)):"No active session"===r.toString()?RMSrv.facebookLoginNShare(e,t,n):void 0}))},qrcodeShare:function(e,t,n){var r,o,i;if(null==n&&(n="id_share_qrcode"),"show"===e){if(r=document.getElementById(n))return r.style.display="block",o=function(){var e;return(e=document.getElementById(n+"_holder")).innerHTML="",new QRCode(e,t)},"undefined"!=typeof QRCode&&null!==QRCode?o():((i=document.createElement("script")).type="text/javascript",i.src="/js/qrcode/qrcode.min.js",document.getElementsByTagName("head")[0].appendChild(i),i.onload=o)}else if(r=document.getElementById(n))return r.style.display="none"},showSMB:function(e){var t,n,r,o,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"share-";return RMSrv._shareMask||(RMSrv._shareMask=document.getElementById("backdrop"),null!=(n=RMSrv._shareMask)&&n.addEventListener("click",(function(){return RMSrv.showSMB("hide")}))),"show"===e?(RMSrv._sharePrefix=i,(t=document.getElementById(i+"placeholder"))&&t.appendChild(document.getElementById("shareDialog")),document.body.classList.add("smb-open"),null!=(r=RMSrv._shareMask)&&(r.style.display="block"),RMSrv.shareLang()):"hide"===e?(document.body.classList.remove("smb-open"),null!=(o=RMSrv._shareMask)?o.style.display="none":void 0):void 0},_shareMap:{title:"title",desc:"description",url:"url",image:"image",data:"data",dnurl:"dnurl"},_shareLang:null,shareLang:function(e){var t,n,r,o,i,a,u,l,c,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.document;return o=document.getElementById("id_share_lang_en"),a=document.getElementById("id_share_lang_zh"),i=document.getElementById("id_share_lang_kr"),r=document.getElementById("id_share_lang_cur"),null!=o&&o.classList.remove("active"),null!=r&&r.classList.remove("active"),null!=a&&a.classList.remove("active"),null!=i&&i.classList.remove("active"),n=null!=r&&null!=(u=r.dataset)?u.lang:void 0,e&&"cur"!==e?"en"===e?null!=o&&o.classList.add("active"):"zh-cn"===e||"zh"===e?null!=a&&a.classList.add("active"):"kr"===e&&null!=i&&i.classList.add("active"):(e=n,null!=r&&r.classList.add("active")),RMSrv._shareMap="en"===e?{"title-en":"title","desc-en":"description",url:"url",image:"image",data:"data",dnurl:"dnurl"}:{title:"title",desc:"description",url:"url",image:"image",data:"data",dnurl:"dnurl"},t=RMSrv._getShareInfo(s,RMSrv.getMeta(s)),null!=(l=document.getElementById("id_share_title"))&&(l.value=t.title),null!=(c=document.getElementById("id_share_desc"))&&(c.value=t.description),RMSrv._shareLang=null!=e&&"cur"!==e?e:null},_getShareInfo:function(e,t){var n,r,o;for(r in n=function(n,r){var o,i;try{if(i=e.getElementById(RMSrv._sharePrefix+n)||e.getElementById("share-"+n)||e.getElementById("alt-"+n))return t[r||n]=i.value||i.textContent}catch(e){return o=e,"undefined"!=typeof console&&null!==console?console.log(o):void 0}},o=RMSrv._shareMap)n(r,o[r]);return null==t.image&&(t.image=RMSrv.getShareImage(e)),null==t.url&&(t.url=e.URL||window.location.href),t},share:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.document;switch(t=function(e,t){var r,o,i,a,u,l,c,s,d,f;i=function(e){return i=null,t(e)},r=RMSrv._getShareInfo(n,RMSrv.getMeta(n)),(d=null!=(l=n.getElementById("id_share_title"))?l.value:void 0)&&(r.title=d),(o=null!=(c=n.getElementById("id_share_desc"))?c.value:void 0)&&(r.description=o),RMSrv._shareLang&&((u=/\?.*(lang\=[a-zA-Z\-]+)/.exec(r.url))?r.url=r.url.replace(u[0],u[0].replace(u[1],"lang=".concat(RMSrv._shareLang))):/\?[a-z0-9]+\=/i.test(r.url)?r.url+="&lang="+RMSrv._shareLang:r.url+="?lang="+RMSrv._shareLang,r.data&&(u=/.*(lang\=[a-zA-Z\-]+)/.exec(r.data))&&(r.data=r.data.replace(u[0],u[0].replace(u[1],"lang=".concat(RMSrv._shareLang)))));try{if(!r.data&&!(r.data=null!=(s=document.querySelector("#share-data"))?s.innerHTML:void 0))return i(r);"string"==typeof e&&(r.data+="&channel=".concat(e)),(f=new XMLHttpRequest).open("POST","/1.5/api/rm/shareInfo",!0),f.setRequestHeader("Content-type","application/x-www-form-urlencoded"),f.timeout=8e3,f.ontimeout=function(){return RMSrv.dialogAlert("Timeout! Try again Later")},f.onreadystatechange=function(){var t;if(4===f.readyState){if(200===f.status)if(t=JSON.parse(f.responseText),"undefined"!=typeof console&&null!==console&&console.log(t.url),t.ok)r.url2=r.url,r.url=t.url;else if(t.err)return RMSrv.dialogAlert(t.err);if(i&&"share"!==e)return i(r)}},f.send(r.data)}catch(e){a=e,"undefined"!=typeof console&&null!==console&&console.log(a),i&&i(r)}},e){case"show":case"hide":return RMSrv.showSMB(e);case"lang-en":return RMSrv.shareLang("en",n);case"lang-cur":return RMSrv.shareLang("cur",n);case"lang-zh-cn":return RMSrv.shareLang("zh-cn",n);case"lang-kr":return RMSrv.shareLang("kr",n);case"qr-code":return RMSrv.showSMB("hide"),t(e,(function(e){return RMSrv.qrcodeShare("show",e.url)}));case"qr-code-close":return RMSrv.qrcodeShare("hide");case"wechat-friend":return RMSrv.showSMB("hide"),t(e,(function(e){return RMSrv.wechatShare(n,e,RMSrv.ver>="3.1.0"?0:WeChat.Scene.session||0)}));case"wechat-moment":return RMSrv.showSMB("hide"),t(e,(function(e){return RMSrv.wechatShare(n,e,RMSrv.ver>="3.1.0"?"undefined"!=typeof Wechat&&null!==Wechat?Wechat.Scene.TIMELINE:void 0:WeChat.Scene.timeline)}));case"facebook-feed":return RMSrv.showSMB("hide"),t(e,(function(e){return RMSrv.facebookShare(n,e,"feed")}));default:return RMSrv.showSMB("hide"),t(e,(function(e){return window.plugins.socialsharing.share(e.title||e.description||"Shared with RealMaster App",e.title||e.description||"RealMaster App Sharing",e.image,e.url)}))}},origin:function(){return window.location.origin||window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:"")},shared:function(e,t){var n,r,o,i,a;return n=function(e){return/propDetailPage/.test(e)?e.split("ec=")[1].split("&")[0]:e.split("?")[0].split("/").pop()},i=RMSrv.origin()+"/1.5/user/update?act=share",null!=e.dnurl&&(i=e.dnurl,/^\//.test(e.dnurl)&&(i=RMSrv.origin()+e.dnurl)),e.url&&(r=n(e.url),o=i.indexOf("?")>0?"&":"?",i+=o+"data="+r,t&&(i+="&pre=1")),(a=new XMLHttpRequest).onreadystatechange=function(){var e,t;if(4===a.readyState&&200===a.status)try{if(null!=(t=e=JSON.parse(a.responseText))?t.j:void 0)return window.location.href=e.j}catch(e){0}},a.open("POST",i,!0),a.send()},clearCache:function(){var e;return null!=(e=window.cache)?e.clear((function(){}),(function(e){return console.log(e)})):void 0},setFileChooser:function(e,t){return null==RMSrv._fileChooser&&(RMSrv._fileChooser={}),RMSrv._fileChooser[e]=t,RMSrv._setFileChooser()},_setFileChooser:function(){var e,t,n,r,o,i;if("android"===("undefined"!=typeof device&&null!==device&&null!=(r=device.platform)?r.toLowerCase():void 0)&&0===("undefined"!=typeof device&&null!==device&&null!=(o=device.version)?o.indexOf("4.4.2"):void 0)){for(n in window.requestFileSystem(LocalFileSystem.PERSISTENT,0,(function(e){return RMSrv.fileSystem=e}),(function(e){return RMSrv.dialogAlert(e)})),i=RMSrv._fileChooser)e=i[n],(t=document.getElementById(n))&&t.addEventListener("click",(function(){return filechooser.open({},(function(t){return RMSrv.fileSystem.root.getFile(t.filepath,null,(function(t){return t.file(e,(function(e){return RMSrv.dialogAlert(e)}))}),(function(e){return RMSrv.dialogAlert(e)}))}))}),(function(e){return RMSrv.dialogAlert(e)}));return delete RMSrv._fileChooser}},getKeyboard:function(e){var t;return("undefined"!=typeof cordova&&null!==cordova&&null!=(t=cordova.plugins)?t.Keyboard:void 0)?e(cordova.plugins.Keyboard):(null==RMSrv._cbKeyBoard&&(RMSrv._cbKeyBoard=[]),RMSrv._cbKeyBoard.push(e))},_getKeyboard:function(){var e,t,n,r;if(("undefined"!=typeof cordova&&null!==cordova&&null!=(n=cordova.plugins)?n.Keyboard:void 0)&&RMSrv._cbKeyBoard){for(e=0,t=(r=RMSrv._cbKeyBoard).length;e<t;e++)(0,r[e])(cordova.plugins.Keyboard);return delete RMSrv._cbKeyBoard}},onReady:function(e){var t=this;return this._readyWaitingList?(this._readyWaitingList.push(e),!0):(this._readyWaitingList=[],this._readyWaitingList.push(e),this.getKeyboard((function(){var e,n,r,o;for(n=0,r=(o=t._readyWaitingList).length;n<r;n++)"function"==typeof(e=o[n])&&e();return delete t._readyWaitingList})),!0)},sendToken:function(e,t){var n,r;return n=e+":"+t,this._fnWhenReg||n!==localStorage.pn?(localStorage.pn=n,(r=new XMLHttpRequest).onreadystatechange=function(){if(4===r.readyState&&200===r.status)return"undefined"!=typeof console&&null!==console?console.log(r.responseText):void 0},r.open("POST","/1.5/user/updPn"),r.setRequestHeader("Content-type","application/x-www-form-urlencoded"),r.send("pn="+n),this._notifyReg(n)):this._notifyReg(n)},regDevice:function(){var e,t;if(this.wakenByMsg)return null;try{return t=window.plugins.pushNotification,"android"===device.platform||"Android"===device.platform||"amazon-fireos"===device.platform?t.register((function(){}),(function(e){return RMSrv.dialogAlert(e)}),{senderID:"339283724232",ecb:"onNotification"}):t.register((function(e){return RMSrv.sendToken("ios",e)}),(function(e){return RMSrv.dialogAlert(e)}),{badge:"true",sound:"true",alert:"true",ecb:"onNotificationAPN"})}catch(t){return e=t,RMSrv.dialogAlert("Error: "+e.message)}},_notifyReg:function(e){var t,n,r,o,i;if(this._RegFinished=!0,n=this._fnWhenReg){for(i=[],r=0,o=n.length;r<o;r++){t=n[r];try{i.push(t(e))}catch(e){0}}return i}},whenReg:function(e){return this._RegFinished?e():(null==this._fnWhenReg&&(this._fnWhenReg=[]),this._fnWhenReg.push(e))},getTranslate:function(e,t){var n;return n="https://translate.google.com/#auto/zh-CN/"+encodeURIComponent(e),RMSrv.getPageContent(n,".text-wrap .translation",{wait:12e3},(function(e){return t(e)}))},getPageContent:function(e,t,n,r){return null==r&&(r=n,n=null),RMSrv.getKeyboard((function(){var o,i,a,u,l,c,s,d,f,h,v,g,p;if(!n.close||!f||"function"!=typeof f.close)return d=cordova.ThemeableBrowser,a={image:"left",imagePressed:"left",align:"left",event:"custClosePressed"},i={image:"back",imagePressed:"back",align:"left",event:"backPressed"},(c=window.isIOS?[a,i]:[i,a]).push({image:"check",imagePressed:"check",align:"right",event:"pagesPressed"}),l={hidden:!0,toolbar:{height:44,color:"#E03131"},customButtons:c,fullscreen:!1},!1===n.hide&&(l.hidden=!1),v=!1,f=d.open(e,"_blank",l),h=function(){return f.removeEventListener("custClosePressed",u),f.removeEventListener("backPressed",o),f.removeEventListener("loaderror",g),f.removeEventListener("pagesPressed",p),f.removeEventListener(cordova.ThemeableBrowser.EVT_ERR,g),f.close()},s=function(e){if(!v)return v=!0,h(),r(e)},!0!==n.nostop&&setTimeout((function(){return p()}),(null!=n?n.wait:void 0)||6e3),p=function(){var e;return e="(el = document.querySelector('".concat(t,"'))?el.textContent:''"),"html"!==t&&"html:wechat"!==t||(e="document.documentElement.innerHTML"),setTimeout((function(){return f.executeScript({code:e},(function(e){return Array.isArray(e)&&(e=e[0]),s(e)}))}),1e3)},g=function(e){return"Error: ".concat(e.code+" : "+e.message)},u=function(){return s("Cancelled")},o=function(){return f.executeScript({code:"window.history.back(-1)"})},(!0===l.hidden||n.isWechat)&&f.addEventListener("loadstop",p),f.addEventListener("custClosePressed",u),f.addEventListener("backPressed",o),f.addEventListener("pagesPressed",p),f.addEventListener("loaderror",g),f.addEventListener(cordova.ThemeableBrowser.EVT_ERR,g);h()}))}},jumpURL=function(e){return window.location=RMSrv.origin()+"/scheme/jump?u="+encodeURIComponent(e)},confirmJump=function(e,t){var n;return n=function(e){if(2===e)return jumpURL(t)},RMSrv.dialogConfirm(e,n,"Message",["Cancel","Open"])},onNotification=function(e){var t,n,r,o,i,a,u;switch(e.event){case"registered":if(RMSrv.wakenByMsg)return;if(e.regid.length>0)return RMSrv.sendToken("android",e.regid);break;case"message":if(RMSrv.wakenByMsg=!0,navigator.vibrate(1e3),e.foreground){if(u=e.soundname||e.payload.sound,new Media("/android_asset/www/"+u).play(),(null!=(t=e.payload)?t.url:void 0)&&(null!=(n=e.payload)?n.message:void 0))return confirmJump(e.payload.message,e.payload.url)}else if(e.coldstart){if(null!=(r=e.payload)?r.url:void 0)return jumpURL(e.payload.url)}else if((null!=(o=e.payload)?o.url:void 0)&&(null!=(i=e.payload)?i.message:void 0))return confirmJump(e.payload.message,e.payload.url);if((null!=(a=e.payload)?a.message:void 0)&&dialogAlert(e.payload.message),"undefined"!=typeof updateNotification&&null!==updateNotification)return updateNotification(e.payload.msgcnt);break;case"error":return RMSrv.dialogAlert(e.msg);default:return RMSrv.dialogAlert("Unknown Event")}},onNotificationAPN=function(e){var t;return("undefined"!=typeof navigator&&null!==navigator?navigator.vibrate:void 0)&&navigator.vibrate(1e3),e.sound&&new Media(e.sound).play(),e.badge&&(t=window.plugins.pushNotification)&&t.setApplicationIconBadgeNumber((function(){}),RMSrv.dialogAlert,e.badge),e.url&&e.alert?e.foreground?confirmJump(e.alert,e.url):jumpURL(e.url):e.alert?RMSrv.dialogAlert(e.alert):void 0},handleOpenURL=function(e){return setTimeout((function(){var t,n;if((t=e.indexOf("?"))>=0)return n=e.substr(t),window.location=RMSrv.origin()+"/scheme"+n;n=""}),10)},window.onerror=function(e,t,n){var r,o,i;o=e+"\n"+t+"\n"+n,/i\.realmaster/.test(window.location.href)&&alert(o);try{return(i=new XMLHttpRequest).onreadystatechange=function(){if(4===i.readyState&&200===i.status)return"undefined"!=typeof console&&null!==console?console.log(i.responseText):void 0},i.open("POST","/cError"),i.setRequestHeader("Content-type","application/x-www-form-urlencoded"),i.send("m="+encodeURIComponent(o))}catch(e){return r=e,"undefined"!=typeof console&&null!==console?console.log(r):void 0}},RMSrv.init();
