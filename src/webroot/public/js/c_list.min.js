"use strict";var app,crmCtrl,goTo;goTo=function(r){return document.getElementById("busy-icon").style.display="block",window.location=r},crmCtrl=function(r,t){var o,e;return r.isArray=angular.isArray,e=document.querySelector("body"),o=document.querySelector(".backdrop"),r.toggleSMB=function(){var r;return e.classList.toggle("smb-open"),r="none"===o.style.display?"block":"none",o.style.display=r},r.goBack=function(){return document.location.href.indexOf("/1.5/more")>0?window.location="/1.5/more":window.location="/1.5/index"},r.learnMore=function(r){var t;return t="https://www.realmaster.ca/membership",r&&(t=r),RMSrv.showInBrowser(t)},r.chat=function(o){if(null!=o?o._id:void 0)return t.post("/chat/api/requestchat",{}).success((function(t){return t.ok?(r.list=t.list||[],window.location="/chat/u/"+o._id):r.toggleSMB("becameVip")})).error((function(){return RMSrv.dialogAlert("Server Error!")}))},t.post("/1.5/crm/leads",{}).success((function(t){return t.err?RMSrv.dialogAlert(err):r.list=t.list||[]}))},(app=angular.module("crmApp",[])).controller("crmCtrl",["$scope","$http",crmCtrl]);
