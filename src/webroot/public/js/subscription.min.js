"use strict";var appProf;(appProf=angular.module("appProfile",[])).controller("ctrlNotifications",["$scope","$http",function(r,t){return r.formData=vars.fld||{},r.loading=!1,r.formData.exuser=vars.exuser,r.i=vars.i||null,Array.isArray(r.i)&&r.i.length>1&&r.i[0]===r.i[1]&&(r.i=r.i[0]),r.linkTo=function(r){return window.open(r,"_blank")},r.curStatus=vars.curStatus,r.showBackdrop=vars.showBackdrop,r.emailStatusList=vars.emailStatus,r.getEmailStatus=function(){if(r.emailStatus=r.emailStatusList[r.curStatus],"unverified"===r.curStatus)return r.emailStatus.description="Complete email verification to receive listing alert",r.emailStatus.word="Unverified"},r.getEmailStatus(),r.setBool=function(e,s){var i,a;return s=1===s?0:1,r.formData[e]=s,(i={})[e]=s,i.i=r.i,(a=t.post("/setting/subscription",i)).success((function(t){return r.message=t.message,t.ok?console.log(t):RMSrv.dialogAlert("Error")})),a.error((function(t){return r.message=t.message,alert("Error when saving pref")}))},r.emailAction=function(e){var s,i,a;if(e=r.emailStatus.action,i={i:r.i},"set"===e)s="resubscribe";else{if("unset"!==e)return;s="unsubscribe"}return i.action=e,(a=t.post("/1.5/settings/receving",i)).success((function(t){return t.ok?(r.curStatus=s,"resubscribe"===s?r.showBackdrop=!0:"unsubscribe"===s&&(r.showBackdrop=!1),r.getEmailStatus(),alert(t.message)):RMSrv.dialogAlert("Error")})),a.error((function(t){return r.message=t.err,alert("Error when saving pref")}))}}]);
