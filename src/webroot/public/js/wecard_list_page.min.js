"use strict";function _typeof(t){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof(t)}var app,toggleModal;toggleModal||(toggleModal=function(t,e){var r;return!!(r=document.getElementById(t))&&("open"===e?r.classList.add("active"):"close"===e?r.classList.remove("active"):(r.classList.toggle("active"),e=r.classList.contains("active")?"open":"close"),e)}),(app=angular.module("app",[])).controller("ctrlWecard",["$scope","$http","$timeout",function(t,e,r){var a,n,o;return n=document.querySelector("body"),t.toggleSMB=function(e){if(n.classList.toggle("smb-open"),t.backdrop=!t.backdrop,e)return t.backdrop=!1},t.learnMore=function(t){var e;return e="https://www.realmaster.ca/membership",t&&(e=t),RMSrv.showInBrowser(e)},t.backdrop=!1,t.goBack=function(t){return window.location=vars.d||"/1.5/index"},t.cards=[],t.meta={tag:{}},t.filtData={},t.curData={},t.realFilt={},t.pageNum=0,t.hasMore=!1,t.tooMany=0,t.toggleModal=function(e,r){return t.backdrop=!t.backdrop,toggleModal(e,r)},t.editCard=function(t){var e;e="/1.5/wecard/edit/".concat(t._id,"?action=edit"),window.location=e},t.hasFilter=function(){var e,r,a,n;for(e in r=t.realFilt)if("object"===_typeof(n=r[e])){for(a in n)if(n[a])return!0}else if(n)return!0;return!1},t.createNewWecard=function(){return 1===t.tooMany?flashMessage("tooMany"):2===t.tooMany?flashMessage("vipTooMany"):t.toggleModal("newWecardModal")},t.showCard=function(e,r){var a,n;return!!e&&(n=function(t,e){var r;for(r in t)if(t[r]){if(!e)return!1;if(e.indexOf(r)<0)return!1}return!0},(!(a=r?t.filtData:t.realFilt).tp||e.tp===a.tp)&&(!(a.rcmd&&!e.rcmd)&&!(a.tag&&!n(a.tag,e.meta.tag))))},t.$watch("filtData",(function(e,r){var a,n,o,i,l;if(t.filterResultCount=0,t.filterResultViews=0,t.filterResultViewsD=0,t.cards){for(l=[],n=0,o=(i=t.cards).length;n<o;n++)a=i[n],t.showCard(a,!0)?(t.filterResultCount+=1,t.filterResultViews+=a.meta.vc||0,l.push(t.filterResultViewsD+=a.meta.vcd||0)):l.push(void 0);return l}}),!0),t.setFilter=function(e,r,a){return"tag"===r?(t.filtData.tag||(t.filtData.tag={}),t.filtData.tag&&1===t.filtData.tag[a]?t.filtData.tag[a]="":t.filtData.tag[a]=1):(t.filtData[r]&&t.filtData[r]===a?t.filtData[r]="":t.filtData[r]=a,e.preventDefault(),!1)},t.clearFilter=function(){return t.filtData={},t.applyFilter(),t.initValues()},t.applyFilter=function(){return t.realFilt=angular.copy(t.filtData),t.meta.length=t.filterResultCount,t.meta.vc=t.filterResultViews,t.meta.vcd=t.filterResultViewsD,t.toggleModal("filterModal")},t.keys=function(t){return(t?Object.keys(t):[]).sort()},t.addTag=function(r){var a,n;if(t.tagInput||r)return(a=t.curData.curCard).meta.tag||(a.meta.tag=[]),n=r||t.tagInput,a.meta.tag.indexOf(n)>-1?flashMessage("tag-exist"):(a.meta.tag.push(n),e.post("/1.5/wecard/prop/update",{_id:a._id,tag:a.meta.tag}).success((function(e,r,a,n){return e.success?(t.tagInput="",t.initTags()):alert(e.err)})).error((function(e,r,a,n){return t.err=e.err,alert("Error when saving tag")})))},t.toggleTag=function(r){var a;return(a=t.curData.curCard).meta.tag||(a.meta.tag=[]),a.meta.tag.indexOf(r)<0?t.addTag(r):(a.meta.tag.splice(r,1),e.post("/1.5/wecard/prop/update",{_id:a._id,tag:a.meta.tag}).success((function(e,r,a,n){return e.success?t.initTags():alert(e.err)})).error((function(e,r,a,n){return t.err=e.err,alert("Error when saving tag")})))},t.$watch("url",(function(e,r){var a;return t.url&&(a=t.url.indexOf("http"))>0&&(t.url=t.url.substr(a)),t.validUrl=/^(http|https):\/\/[^ "]+$/.test(t.url)})),a=function(){if(t.url&&t.validUrl)return t.url=encodeURIComponent(t.url),window.location="/1.5/wecard/edit/topic?url=".concat(t.url)},t.dialogAlertVip=function(){var t,e,r,a;return a=vars.vipTipStr||"Available only for Premium VIP user! Upgrade and get more advanced features.",e=vars.vipLaterStr||"Later",r=vars.vipSeeStr||"See More",t=function(t){if(t+""=="2")return RMSrv.showInBrowser("https://www.realmaster.ca/membership")},RMSrv.dialogConfirm(a,t,"VIP",[e,r])},t.editURL=function(){vars.isVipUser?a():t.dialogAlertVip()},t.scanQR=function(){RMSrv.scanQR((function(e){return t.url=e,t.$apply()}))},t.initValues=function(e){o(),t.initTags(e)},o=function(){var e,r,a,n;for(t.meta.length=t.total?t.total:t.cards.length,t.meta.vc=0,t.meta.vcd=0,r=0,a=(n=t.cards).length;r<a;r++)e=n[r],t.meta.vc+=parseInt(e.meta.vc)||0,t.meta.vcd+=parseInt(e.meta.vcd)||0},t.initTags=function(e){var r,a,n,o,i,l,s,u;for(e&&(t.meta.tag={}),a=0,o=(l=t.cards).length;a<o;a++)if((r=l[a]).meta.tag)for(n=0,i=(s=r.meta.tag).length;n<i;n++)u=s[n],t.meta.tag[u]=""},t.showMore=function(){return t.pageNum+=1,t.getWepages({page:t.pageNum}),null},t.setTooMany=function(e){if(null!=e.tooMany)return t.tooMany=e.tooMany},t.setTotal=function(e){if(null!=e.total)return t.total=e.total},t.getWepages=function(){var a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};t.loading=!0,e.post("/1.5/wecard",a).success((function(e,n,o,i){return r((function(){return t.loading=!1}),200),e.success?(a.page?t.cards=t.cards.concat(e.l):t.cards=e.l,e.l.length<20?t.hasMore=!1:t.hasMore=!0,t.setTooMany(e),t.setTotal(e),t.initValues()):alert(e.err)})).error((function(e,r,a,n){return t.err=e.err,t.loading=!1,alert("Error when saving tag")}))},t.getWepages()}]),app.controller("ctrlWecardLiitem",["$scope","$http",function(t,e){return t.getMetaImage=function(e,r){var a;return(a=r.meta.img)?t.realSrc?t.realSrc:/realmaster|mmbiz/.test(a)?a:(setTimeout((function(){if(t.realSrc!==a)return t.realSrc=a,t.$apply()}),300),"/img/loading.png"):"/img/noPic.png"},t.realSrc=null,t.del=!1,t.toggleApprove=function(e){return t.del?t.del=!1:t.del=!0,e.preventDefault(),e.stopPropagation(),!1},t.recommend=function(r,a){var n,o,i;return i=0,null!=(n=t.cards[a]).rcmd?(i=1,n.rcmd=null):n.rcmd=1,(o=e.post("/1.5/wecard/recommend",{tp:n.tp,_id:n._id,ml_num:n.ml_num,unset:i})).success((function(e){if(t.message=e.message,!e.success)return RMSrv.dialogAlert(e.message)})),o.error((function(e){return t.message=e.message,RMSrv.alert("Error when recommending")})),r.preventDefault(),r.stopPropagation(),!1},t.createCLone=function(r,a){var n,o;return n=t.cards[a],(o=e.post("/1.5/wecard/clone",{_id:n._id})).success((function(e){return t.message=e.message,e.success?t.$parent.getWepages():RMSrv.dialogAlert(e.err)})),o.error((function(e){return t.message=e.message,RMSrv.dialogAlert("Error when cloneing")})),r.preventDefault(),r.stopPropagation(),!1},t.delete=function(r,a){var n,o,i,l,s,u,c;return c=vars.strDeleteTip,o=vars.strCancle,l=vars.strConfirm,i=t.cards[a],s=vars.no_permission,n=function(r){var n;if(r+""=="2")return(n=e.post("/1.5/wecard/delete",{tp:i.tp,_id:i._id,ml_num:i.ml_num})).success((function(e,r,n,o){return t.message=e.message,e.success?(t.del=!1,t.cards.splice(a,1),t.setTooMany(e),t.setTotal(e),t.initValues(!0)):alert(e.message)})),n.error((function(e,r,a,n){return t.message=e.message,alert("Error when saving user profile")}))},/^\:/.test(null!=(u=i.meta)?u.editor:void 0)?vars.isAdmin?RMSrv.dialogConfirm(c,n,"",[o,l]):RMSrv.dialogAlert(s):n(2),r.preventDefault(),r.stopPropagation(),!1}}]);
