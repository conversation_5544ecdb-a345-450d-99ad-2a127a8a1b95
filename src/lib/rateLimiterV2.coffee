### Limit Access Rate Automatically
allows different limit for different request endpoints

NOTE:测试环境需要去掉配置文件中limitAccessRate参数,防止localhost blocked

Collect every 47s:
 1. as = access/session
 2. avgas = avg(access/session)/ip
 3. s = session/ip

block Criteria:
  for any ip
    if s > 20 && avgas < 2
  for any session
    if as > max(70, avg(max(as)) * 2)

###

util = require 'util'
libDebug = require './debug'
{stringify} = require './helpers_string'
{isSearchEngine} = require './helpers'
debug = libDebug.getDebugger()
verbose = 0

gInstances = {} # 保存所有实例

# allow localhost
# NOTE: need to fix unittest, if block localhost will cause unittest fail
# '127.0.0.1'
defaultWhiteListIPs =  []
defaultConfig =
  AccessPerSessionLimit: 350
  SessionPerIPThreshold: 350
  AvgAccessPerSessionIP: 3
  FormInputPerIPThreshold: 8
  FormInputPerSessionThreshold: 6
  AllowSearchEngine: true
  WhiteListIPs: defaultWhiteListIPs
  # 爬虫检测相关配置
  MaxPageNumber: 100              # 最大页码限制
  PageSequenceThreshold: 16        # 连续翻页次数阈值
  HighPageAccessThreshold: 5      # 高页码访问次数阈值
  PageAccessWindowTime: 120000    # 页码访问记录窗口时间（毫秒），默认2分钟
  ScraperDetectionEnabled: false  # 是否启用爬虫检测,默认关闭
  HighPagePercentage: 0.5         # 高页码百分比

blackListIPs = {}
whiteListIPs = {}

OVER_LIMIT_ERR = 'Visit the page multiple times in a short time!'
returnError = (req,resp)->
  # NOTE: no view exists to ckup
  # ERR@2023-04-06T16:51:29.411:Cannot read property 'widgets' of undefinedtemplate error in view/widget 'generalError' in layout 'undefined' for URL /zh-cn/for-sale/Barrie/view=map.prov=ON.ptype=Residential.src=mls.cmty=Grove%20East.page=0
  # if req.method is 'GET'
  #   resp.ckup 'generalError',{err:OVER_LIMIT_ERR}
  # else {err:OVER_LIMIT_ERR}
  # NOTE: should use 429
  resp.send '',403

# 需要忽略的路径列表
IGNORED_PATHS = ['1.5/translate', '1.5/pageData', '1.5/stdfun']

class RateLimiter
  constructor:(config)->
    @options = Object.assign {},defaultConfig
    for key of defaultConfig
      @options[key] = config[key] if config[key]?
      
    @AlertHandler = (msg)-> debug.error msg
    @accessPerSessions = {}
    @sessionInIPs = {}
    @blockedIPs = {}
    @blockedSessions = {}
    @formInputPerSessions = {}
    @formInputPerIPs = {}
    @formBlockedIPs = {}
    @formBlockedSessions = {}
    # 爬虫检测相关数据结构
    @pageAccessRecords = {}       # 存储页码访问记录 {sid: {timestamp: [页码访问记录]}}
    @ipPageAccessRecords = {}     # 存储IP页码访问记录 {ip: {timestamp: [页码访问记录]}}

  ###
  # @description 检测是否为爬虫行为
  # @param {string} ip - 用户IP
  # @param {string} sid - 会话ID
  # @param {object} req - 请求对象
  # @return {boolean} - 是否为爬虫行为
  ###
  detectScraper: (ip, sid, req) =>
    return false unless @options.ScraperDetectionEnabled
    
    # 白名单IP不检测
    return false if ip in @options.WhiteListIPs
    
    # 搜索引擎不检测
    return false if @options.AllowSearchEngine and isSearchEngine(req) #/google|bing|baidu|yahoo/i.test req.UA()
    
    # 解析请求中的页码
    pageNum = @extractPageNumber(req)
    return false unless pageNum?
    
    # 高页码检测
    if pageNum > @options.MaxPageNumber
      alertMsg = "Blocked IP[#{ip}] Session[#{sid}] for accessing high page number: #{pageNum} > #{@options.MaxPageNumber}"
      @AlertHandler alertMsg, false, true
      @blockIPAndSession(ip, sid, "High page number access: #{pageNum}")
      return true
    
    # 记录页码访问 
    currentTime = Date.now()
    @recordPageAccess(ip, sid, pageNum, currentTime)
    
    # 检测连续页码访问
    if @detectSequentialPageAccess(ip, sid)
      alertMsg = "Blocked IP[#{ip}] Session[#{sid}] for sequential page access pattern"
      @AlertHandler alertMsg, false, true
      @blockIPAndSession(ip, sid, "Sequential page access pattern")
      return true
    
    # 检测高频高页码访问
    if @detectHighPageFrequency(ip, sid)
      alertMsg = "Blocked IP[#{ip}] Session[#{sid}] for high frequency page access"
      @AlertHandler alertMsg, false, true
      @blockIPAndSession(ip, sid, "High frequency page access")
      return true
    
    return false

  ###
  # @description 从请求中提取页码
  # @param {object} req - 请求对象
  # @return {number|null} - 页码数字或null
  ###
  extractPageNumber: (req) =>
    pageStr = null
    
    # 从URL查询参数中提取
    if req.query?.page?
      pageStr = req.query.page
    
    # 从URL路径中提取（如 /page=123/ 格式）
    else if req.path?
      pageMatch = req.path.match(/[?&]page=(\d+)|\/page=(\d+)/)
      if pageMatch
        pageStr = pageMatch[1] or pageMatch[2]
    
    # 从body中提取（针对POST请求）
    else if req.body?.page?
      pageStr = req.body.page
    
    return null unless pageStr?
    
    # 转换为数字
    page = parseInt(pageStr, 10)
    return if isNaN(page) then null else page

  ###
  # @description 记录页码访问
  # @param {string} ip - 用户IP
  # @param {string} sid - 会话ID
  # @param {number} pageNum - 页码
  # @param {number} timestamp - 时间戳
  ###
  recordPageAccess: (ip, sid, pageNum, timestamp) =>
    # 记录会话的页码访问
    if sid
      @pageAccessRecords[sid] ?= []
      @pageAccessRecords[sid].push({page: pageNum, time: timestamp})
      
      # 清理过期记录
      @pageAccessRecords[sid] = @pageAccessRecords[sid].filter (record) =>
        return timestamp - record.time < @options.PageAccessWindowTime
    
    # 记录IP的页码访问
    @ipPageAccessRecords[ip] ?= []
    @ipPageAccessRecords[ip].push({page: pageNum, time: timestamp})
    
    # 清理过期记录
    @ipPageAccessRecords[ip] = @ipPageAccessRecords[ip].filter (record) =>
      return timestamp - record.time < @options.PageAccessWindowTime

  ###
  # @description 检测连续页码访问
  # @param {string} ip - 用户IP
  # @param {string} sid - 会话ID
  # @return {boolean} - 是否检测到连续页码访问
  ###
  detectSequentialPageAccess: (ip, sid) =>
    records = []
    
    # 检查会话记录
    if sid and @pageAccessRecords[sid]?.length >= @options.PageSequenceThreshold
      records = @pageAccessRecords[sid]
    # 检查IP记录
    else if @ipPageAccessRecords[ip]?.length >= @options.PageSequenceThreshold
      records = @ipPageAccessRecords[ip]
    
    return false if records.length < @options.PageSequenceThreshold
    
    # 按时间排序
    sortedRecords = records.sort (a, b) -> a.time - b.time
    
    # 提取页码序列
    pages = sortedRecords.map (r) -> r.page
    
    # 检测连续递增或递减序列
    sequenceLength = 1
    direction = 0  # 0=未定，1=递增，-1=递减
    
    for i in [1...pages.length]
      diff = pages[i] - pages[i-1]
      
      # 第一次确定方向
      if direction == 0 and diff != 0
        direction = if diff > 0 then 1 else -1
        sequenceLength = 2
      # 符合当前方向
      else if (direction == 1 and diff > 0) or (direction == -1 and diff < 0)
        sequenceLength++
      # 方向改变或无变化
      else
        # 如果序列足够长，返回检测到
        if sequenceLength >= @options.PageSequenceThreshold
          return true
        # 重置序列
        sequenceLength = 1
        direction = 0
    
    # 最后检查一次
    return sequenceLength >= @options.PageSequenceThreshold

  ###
  # @description 检测高频高页码访问
  # @param {string} ip - 用户IP
  # @param {string} sid - 会话ID
  # @return {boolean} - 是否检测到高频高页码访问
  ###
  detectHighPageFrequency: (ip, sid) =>
    records = []
    
    # 检查会话记录
    if sid and @pageAccessRecords[sid]
      records = @pageAccessRecords[sid]
    # 检查IP记录
    else if @ipPageAccessRecords[ip]
      records = @ipPageAccessRecords[ip]
    
    return false if records.length < @options.HighPageAccessThreshold
    
    # 计算高页码访问次数
    highPageCount = 0
    for record in records
      if record.page >= @options.MaxPageNumber * @options.HighPagePercentage
        highPageCount++
    
    # 高页码访问次数超过阈值
    return highPageCount >= @options.HighPageAccessThreshold

  ###
  # @description 拉黑IP和会话
  # @param {string} ip - 用户IP
  # @param {string} sid - 会话ID
  # @param {string} reason - 拉黑原因
  ###
  blockIPAndSession: (ip, sid, reason = '') =>
    if ip
      @blockedIPs[ip] = true
      debug.info "Blocked IP[#{ip}] for scraping: #{reason}"
    
    if sid
      @blockedSessions[sid] = true
      debug.info "Blocked Session[#{sid}] for scraping: #{reason}"

  checkAccessRate: =>
    blocked = ''
    for ip,sessions of @sessionInIPs
      session_cnt = 0
      access_cnt = 0
      for sid,cnt of sessions
        session_cnt++
        access_cnt += cnt
      avgas = access_cnt / session_cnt
      if (session_cnt > @options.SessionPerIPThreshold) and (avgas < @options.AvgAccessPerSessionIP)
        if ip in @options.WhiteListIPs
          continue
        blocked += ' ' + ip
        @blockedIPs[ip] = true
        @AlertHandler "Blocked IP[#{ip}] Sessions:#{session_cnt} AvgAccessPerSession:#{avgas}",false,true
    for sid,slog of @accessPerSessions
      if slog.c > @options.AccessPerSessionLimit
        blocked += ' ' + sid
        @blockedSessions[sid] = true
        @AlertHandler "Blocked Session[#{sid}]#{slog.c} user:#{slog.eml or slog.uid} ver:#{slog.ver} ua:#{slog.ua} IPs:#{util.inspect(slog.ips)}",false,true
    if blocked
      debug.info 'rateLimiter blocked:' + blocked
      debug.info util.inspect @sessionInIPs
      debug.info util.inspect @accessPerSessions
    @accessPerSessions = {}
    @sessionInIPs = {}
    @formInputPerSessions = {}
    @formInputPerIPs = {}
    
    # 每次检查时清理过期的页码访问记录
    @cleanPageAccessRecords()

  ###
  # @description 清理过期的页码访问记录
  ###
  cleanPageAccessRecords: =>
    currentTime = Date.now()
    expireTime = currentTime - @options.PageAccessWindowTime
    
    # 清理会话页码记录
    for sid, records of @pageAccessRecords
      @pageAccessRecords[sid] = records.filter (r) -> r.time > expireTime
      # 删除空记录
      if @pageAccessRecords[sid].length == 0
        delete @pageAccessRecords[sid]
    
    # 清理IP页码记录
    for ip, records of @ipPageAccessRecords
      @ipPageAccessRecords[ip] = records.filter (r) -> r.time > expireTime
      # 删除空记录
      if @ipPageAccessRecords[ip].length == 0
        delete @ipPageAccessRecords[ip]

  countAndReturnFormInputBlocked: (ip,sid,msg)=>
    # check ip
    
    if @formBlockedIPs[ip]
      return true
    if not @formInputPerIPs[ip]?
      @formInputPerIPs[ip] = 1
    if (@formInputPerIPs[ip]++) >= @options.FormInputPerIPThreshold
      alertMsg = "Blocked IP[#{ip}] FormInput #{@formInputPerIPs[ip]} > #{@options.FormInputPerIPThreshold}"
      alertMsg += '\n Body:' + msg if msg
      @AlertHandler alertMsg,false,true
      @formBlockedIPs[ip] = true
      return true
    # check session
    unless sid
      return false
    if @formBlockedSessions[sid]
      return true
    if not @formInputPerSessions[sid]?
      @formInputPerSessions[sid] = 1
      return false
    if (@formInputPerSessions[sid]++) >= @options.FormInputPerSessionThreshold
      alertMsg = "Blocked Session[#{sid}] FormInput #{@formInputPerSessions[sid]} > #{@options.FormInputPerSessionThreshold}"
      alertMsg += '\n Body:' + msg if msg
      @AlertHandler alertMsg,false,true
      @formBlockedSessions[sid] = true
      return true
    return false

  getCountAndReturnFormInputBlockedFunction: ->
    fn = @countAndReturnFormInputBlocked
    return ->
      ip = @remoteIP()
      sid = @session?.id()
      body = @body
      if body
        body = stringify body
      fn(ip,sid,body)

  resetBlockedIPs: =>
    @blockedIPs = {}
    @blockedSessions = {}
    @formBlockedIPs = {}
    @formBlockedSessions = {}
    # NOTE:需要将下面两个参数重置,否则依然会被block
    @formInputPerSessions = {}
    @formInputPerIPs = {}
    # 重置爬虫检测相关数据
    @pageAccessRecords = {}
    @ipPageAccessRecords = {}

  setAlertHandler:(handler)->
    @AlertHandler = handler


exports.RateLimiter = RateLimiter

###
# @params
    type config {
      AccessPerSessionLimit? : number
      SessionPerIPThreshold? : number
      AvgAccessPerSessionIP? : number
      FormInputPerIPThreshold? : number
      FormInputPerSessionThreshold? : number
      AllowSearchEngine? : boolean
      WhiteListIPs? : array of string
      MaxPageNumber? : number
      PageSequenceThreshold? : number
      HighPageAccessThreshold? : number
      PageAccessWindowTime? : number
      ScraperDetectionEnabled? : boolean
      source  : string  # required, 创建rateLimiter实例来源
    }
###
exports.createLimiter = createLimiter = (config,handler = null)->
  rateLimiter = new RateLimiter(config)
  rateLimiter.setAlertHandler handler if handler
  setInterval rateLimiter.checkAccessRate, 47000 # 47s
  setInterval rateLimiter.resetBlockedIPs, (3600000 * 24) # 1 day
  gInstances[config.source or Date.now()] = rateLimiter # 存储instance
  return rateLimiter
###
# @params
    type rateLimiterInstance : RateLimiter
this returns a middleware function
###
exports.createFilter = createFilter = (rateLimiterInstance)->
  return (req, resp, next)->
    ip = req.remoteIP()
    if whiteListIPs[ip]
      return next()
    if blackListIPs[ip]
      return returnError req,resp
    
    # 检查请求路径是否在忽略列表中
    if IGNORED_PATHS.includes(req.pathname)
      return next()

    if rateLimiterInstance.blockedIPs[ip]
      return returnError req,resp

    # insert counter function
    if not req.countAndReturnFormInputBlocked
      req.countAndReturnFormInputBlocked = rateLimiterInstance.getCountAndReturnFormInputBlockedFunction()

    # 爬虫检测
    sid = req.session?.id()
    if rateLimiterInstance.detectScraper(ip, sid, req)
      return returnError req,resp

    if rateLimiterInstance.options.AllowSearchEngine and isSearchEngine(req)#/google|bing|baidu|yahoo/i.test req.UA()
      return next()

    unless sid
      return next()
    if rateLimiterInstance.blockedSessions[sid]
      return returnError req,resp

    sessionLog = (rateLimiterInstance.accessPerSessions[sid] ?= {ips:{},c:0,ver:req.cookies?['apsv'],ua:req.UA()})
    req.getLoggedInUid req, (uid)->
      if uid
        sessionLog.uid = uid
      sessionLog.c++
      if sessionLog.ips[ip]
        sessionLog.ips[ip]++
      else
        sessionLog.ips[ip] = 1

      sessions = (rateLimiterInstance.sessionInIPs[ip] ?= {})
      if sessions[sid]
        sessions[sid]++
      else
        sessions[sid] = 1

      return next()

module.exports.initBlackListIPs = (data)->
  for k of data
    if /^[0-9]+\_/.test(k) or /[a-fA-F\d]{1,4}:/.test(k)
      k = k.replace(/\_/g,'.')
      blackListIPs[k] = true

module.exports.addBlackListIP = (ip)->
  blackListIPs[ip] = true
module.exports.addWhiteListIP = (ip)->
  whiteListIPs[ip] = true
  
# 获取rateLimiter实例
module.exports.getInstance = (source)->
  return gInstances[source]

# 重置所有rateLimiter实例ip,session黑名单
module.exports.resetAllBlockedIPs = ()->
  for k,v of gInstances
    v.resetBlockedIPs()
  return