<!-- Generated by SpecStory -->

# Make source field mandatory in editCrm (2025-08-28 08:46Z)

_**User**_

@crm.vue editCrm时，如果满足source显示条件，增加source为必填信息

---

_**Assistant**_

I'll help you add source as a required field when the source display condition is met in the `editCrm` method. Let me first examine the current code to understand the structure and then make the necessary changes.


---

Read file: appweb/src/coffee4client/components/contactCrm/crm.vue

---

Now let me look at the data section to see if there's already an error variable for source:


---

Read file: appweb/src/coffee4client/components/contactCrm/crm.vue

---

Now let me check the template to see how source validation errors are displayed:


---

Read file: appweb/src/coffee4client/components/contactCrm/crm.vue

---

Now I understand the structure. I need to:

1. Add an `errSource` error variable to the data section
2. Add source validation in the `editCrm` method
3. Add error display in the template for the source field
4. Update the validation logic to include source validation

Let me make these changes:


---

<details><summary>Edit file: appweb/src/coffee4client/components/contactCrm/crm.vue</summary>

undefined

**Chunk 1**
Lines added: 1, lines removed: undefined

```diff
@@ -168,4 +168,5 @@
        errm:false,
        errCoeml:false,
+       errSource:false,
        fromUrl:false,
        strings:{
```

</details>

---

<details><summary>Edit file: appweb/src/coffee4client/components/contactCrm/crm.vue</summary>

undefined

**Chunk 1**
Lines added: 1, lines removed: undefined

```diff
@@ -40,4 +40,5 @@
            a.btn.btn-default(v-for="source in sourceList", href='javascript:void 0', @click="setSource(source)", :class="{active:contact.source == source}")
              | {{_(source)}}
+         span.inline-error.fa.fa-exclamation-circle(v-show='errSource')
      div.editCell
        input(:placeholder="_('Email','contactCrm')",v-model='contact.eml',v-validate="'email'",@input="verifyEmail()",@blur='contact.eml = checkInput(contact.eml)')
```

</details>

---

<details><summary>Edit file: appweb/src/coffee4client/components/contactCrm/crm.vue</summary>

undefined

**Chunk 1**
Lines added: 2, lines removed: 1

```diff
@@ -357,5 +357,6 @@
        if(this.contact.m && this.contact.m.length>0 && textPattern.test(this.contact.m) == true) this.errm=true;else this.errm = false
        if(this.contact.coeml && this.contact.coeml.length>0 && emlPattern.test(this.contact.coeml) == false) this.errCoeml=true;else this.errCoeml = false
-       if(this.errnm || this.errmbl ||this.erreml || this.errm || this.errCoeml) return
+       if((this.dispVar.isRealGroup || this.dispVar.isDevGroup) && (!this.contact.source || this.contact.source == '')) this.errSource=true;else this.errSource = false
+       if(this.errnm || this.errmbl ||this.erreml || this.errm || this.errCoeml || this.errSource) return
        self.loading = true;
        if(this.viewedUid){
```

</details>

---

<details><summary>Edit file: appweb/src/coffee4client/components/contactCrm/crm.vue</summary>

undefined

**Chunk 1**
Lines added: 1, lines removed: undefined

```diff
@@ -292,4 +292,5 @@
        this.errm = false;
        this.errCoeml = false;
+       this.errSource = false;
      },
      addOrUp(val){
```

</details>

---

<details><summary>Edit file: appweb/src/coffee4client/components/contactCrm/crm.vue</summary>

undefined

**Chunk 1**
Lines added: 1, lines removed: undefined

```diff
@@ -244,4 +244,5 @@
      setSource(source){
        this.contact.source = source;
+       this.errSource = false;
        this.$forceUpdate();
      },
```

</details>

---

Perfect! I've successfully added source validation as a required field when the source display condition is met. Here's a summary of the changes I made:

## Changes Made:

1. **Added error variable**: Added `errSource:false` to the data section to track source validation errors.

2. **Added error display**: Added an error icon (`span.inline-error.fa.fa-exclamation-circle`) to the source field in the template that shows when `errSource` is true.

3. **Added validation logic**: In the `editCrm` method, added validation to check if source is required (when `dispVar.isRealGroup` or `dispVar.isDevGroup` is true) and if the source field is empty or not selected.

4. **Updated validation return condition**: Modified the validation return condition to include `this.errSource` so the form submission is blocked when source validation fails.

5. **Reset error on source selection**: Updated the `setSource` method to reset the `errSource` flag when a user selects a source, providing immediate feedback.

6. **Reset error on popup close**: Updated the `closePopup` method to reset the `errSource` flag when the popup is closed.

## How it works:

- When the source field is displayed (when `dispVar.isRealGroup || dispVar.isDevGroup` is true), the source becomes a required field
- If the user tries to submit the form without selecting a source, an error icon will appear next to the source field
- The form submission will be blocked until a valid source is selected
- When the user selects a source, the error immediately disappears
- The error is also cleared when the popup is closed

The implementation follows the same pattern as other required fields in the component (name, email, phone, etc.) and integrates seamlessly with the existing validation system.

---

